-------------------------------------
Translated Report (Full Report Below)
-------------------------------------
Process:             Runner [91929]
Path:                /Users/<USER>/Library/Developer/CoreSimulator/Devices/24942F86-3060-4B2A-962B-60BD9614F1F4/data/Containers/Bundle/Application/13FF1FEF-7257-49BB-8287-1FCD0810CD0E/Runner.app/Runner
Identifier:          com.hopenapp.hopen
Version:             1.0.0 (1)
Code Type:           ARM-64 (Native)
Role:                Foreground
Parent Process:      launchd_sim [68304]
Coalition:           com.apple.CoreSimulator.SimDevice.24942F86-3060-4B2A-962B-60BD9614F1F4 [103843]
Responsible Process: SimulatorTrampoline [12609]
User ID:             501

Date/Time:           2025-07-19 22:34:18.1839 -0400
Launch Time:         2025-07-19 22:34:14.5923 -0400
Hardware Model:      MacBookPro18,3
OS Version:          macOS 26.0 (25A5306g)
Release Type:        User

Crash Reporter Key:  65553204-4A28-33BB-B7F3-8A48D7F2DE89
Incident Identifier: BD82B5BE-4845-4E46-8B89-8EC915C7A70F

Sleep/Wake UUID:       3E9C9FEE-CE70-435F-BB7B-7D695E48AC2B

Time Awake Since Boot: 140000 seconds
Time Since Wake:       43291 seconds

System Integrity Protection: enabled

Triggered by Thread: 0

Exception Type:    EXC_CRASH (SIGABRT)
Exception Codes:   0x0000000000000000, 0x0000000000000000

Termination Reason:  Namespace SIGNAL, Code 6, Abort trap: 6
Terminating Process: Runner [91929]


Last Exception Backtrace:
0   CoreFoundation                	       0x1804c97c8 __exceptionPreprocess + 160
1   libobjc.A.dylib               	       0x1800937cc objc_exception_throw + 72
2   CoreFoundation                	       0x1804c96e4 -[NSException initWithCoder:] + 0
3   FirebaseCore                  	       0x104ec132c +[FIRApp configure] + 120 (FIRApp.m:115)
4   Runner.debug.dylib            	       0x104f0cc00 closure #2 in AppDelegate.application(_:didFinishLaunchingWithOptions:) + 148 (AppDelegate.swift:49)
5   Runner.debug.dylib            	       0x104f0cf1c thunk for @escaping @callee_guaranteed () -> () + 48
6   libdispatch.dylib             	       0x18017c788 _dispatch_call_block_and_release + 24
7   libdispatch.dylib             	       0x180197278 _dispatch_client_callout + 12
8   libdispatch.dylib             	       0x1801b300c <deduplicated_symbol> + 24
9   libdispatch.dylib             	       0x18018c1c4 _dispatch_main_queue_drain + 1184
10  libdispatch.dylib             	       0x18018bd14 _dispatch_main_queue_callback_4CF + 40
11  CoreFoundation                	       0x180428e9c __CFRUNLOOP_IS_SERVICING_THE_MAIN_DISPATCH_QUEUE__ + 12
12  CoreFoundation                	       0x1804238a8 __CFRunLoopRun + 1920
13  CoreFoundation                	       0x180422cec CFRunLoopRunSpecific + 536
14  GraphicsServices              	       0x191004d00 GSEventRunModal + 164
15  UIKitCore                     	       0x185c597d4 -[UIApplication _run] + 796
16  UIKitCore                     	       0x185c5dba0 UIApplicationMain + 124
17  UIKitCore                     	       0x185023f1c 0x184dfb000 + 2264860
18  Runner.debug.dylib            	       0x104f13fe8 static UIApplicationDelegate.main() + 128
19  Runner.debug.dylib            	       0x104f13f58 static AppDelegate.$main() + 44
20  Runner.debug.dylib            	       0x104f148bc __debug_main_executable_dylib_entry_point + 28
21  ???                           	       0x104be53d4 ???
22  dyld                          	       0x104ce0924 start + 6400

Thread 0 Crashed:
0   libsystem_kernel.dylib        	       0x10632c85c __pthread_kill + 8
1   libsystem_pthread.dylib       	       0x105b6a2ec pthread_kill + 264
2   libsystem_c.dylib             	       0x180171ea8 abort + 100
3   libc++abi.dylib               	       0x1802b0144 abort_message + 128
4   libc++abi.dylib               	       0x18029fe4c demangling_terminate_handler() + 296
5   libobjc.A.dylib               	       0x18006f220 _objc_terminate() + 124
6   libc++abi.dylib               	       0x1802af570 std::__terminate(void (*)()) + 12
7   libc++abi.dylib               	       0x1802b2498 __cxxabiv1::failed_throw(__cxxabiv1::__cxa_exception*) + 32
8   libc++abi.dylib               	       0x1802b2478 __cxa_throw + 88
9   libobjc.A.dylib               	       0x180093904 objc_exception_throw + 384
10  CoreFoundation                	       0x1804c96e4 +[NSException raise:format:] + 124
11  FirebaseCore                  	       0x104ec132c +[FIRApp configure] + 120 (FIRApp.m:110)
12  Runner.debug.dylib            	       0x104f0cc00 closure #2 in AppDelegate.application(_:didFinishLaunchingWithOptions:) + 148 (AppDelegate.swift:49)
13  Runner.debug.dylib            	       0x104f0cf1c thunk for @escaping @callee_guaranteed () -> () + 48
14  libdispatch.dylib             	       0x18017c788 _dispatch_call_block_and_release + 24
15  libdispatch.dylib             	       0x180197278 _dispatch_client_callout + 12
16  libdispatch.dylib             	       0x1801b300c <deduplicated_symbol> + 24
17  libdispatch.dylib             	       0x18018c1c4 _dispatch_main_queue_drain + 1184
18  libdispatch.dylib             	       0x18018bd14 _dispatch_main_queue_callback_4CF + 40
19  CoreFoundation                	       0x180428e9c __CFRUNLOOP_IS_SERVICING_THE_MAIN_DISPATCH_QUEUE__ + 12
20  CoreFoundation                	       0x1804238a8 __CFRunLoopRun + 1920
21  CoreFoundation                	       0x180422cec CFRunLoopRunSpecific + 536
22  GraphicsServices              	       0x191004d00 GSEventRunModal + 164
23  UIKitCore                     	       0x185c597d4 -[UIApplication _run] + 796
24  UIKitCore                     	       0x185c5dba0 UIApplicationMain + 124
25  UIKitCore                     	       0x185023f1c 0x184dfb000 + 2264860
26  Runner.debug.dylib            	       0x104f13fe8 static UIApplicationDelegate.main() + 128
27  Runner.debug.dylib            	       0x104f13f58 static AppDelegate.$main() + 44
28  Runner.debug.dylib            	       0x104f148bc __debug_main_executable_dylib_entry_point + 28
29  ???                           	       0x104be53d4 ???
30  dyld                          	       0x104ce0924 start + 6400

Thread 1:
0   libsystem_pthread.dylib       	       0x105b65984 start_wqthread + 0

Thread 2:
0   libsystem_pthread.dylib       	       0x105b65984 start_wqthread + 0

Thread 3:
0   libsystem_pthread.dylib       	       0x105b65984 start_wqthread + 0

Thread 4:
0   libsystem_pthread.dylib       	       0x105b65984 start_wqthread + 0

Thread 5:
0   libsystem_pthread.dylib       	       0x105b65984 start_wqthread + 0

Thread 6:: com.apple.uikit.eventfetch-thread
0   libsystem_kernel.dylib        	       0x106324b70 mach_msg2_trap + 8
1   libsystem_kernel.dylib        	       0x10633590c mach_msg2_internal + 72
2   libsystem_kernel.dylib        	       0x10632cc10 mach_msg_overwrite + 480
3   libsystem_kernel.dylib        	       0x106324ee4 mach_msg + 20
4   CoreFoundation                	       0x180428bc4 __CFRunLoopServiceMachPort + 156
5   CoreFoundation                	       0x1804235a4 __CFRunLoopRun + 1148
6   CoreFoundation                	       0x180422cec CFRunLoopRunSpecific + 536
7   Foundation                    	       0x180f0e38c -[NSRunLoop(NSRunLoop) runMode:beforeDate:] + 208
8   Foundation                    	       0x180f0e5ac -[NSRunLoop(NSRunLoop) runUntilDate:] + 60
9   UIKitCore                     	       0x185d07390 -[UIEventFetcher threadMain] + 408
10  Foundation                    	       0x180f35148 __NSThread__start__ + 716
11  libsystem_pthread.dylib       	       0x105b6a5f0 _pthread_start + 104
12  libsystem_pthread.dylib       	       0x105b65998 thread_start + 8

Thread 7:
0   libsystem_pthread.dylib       	       0x105b65984 start_wqthread + 0

Thread 8:
0   libsystem_pthread.dylib       	       0x105b65984 start_wqthread + 0

Thread 9:
0   libsystem_pthread.dylib       	       0x105b65984 start_wqthread + 0

Thread 10:
0   libsystem_pthread.dylib       	       0x105b65984 start_wqthread + 0

Thread 11:
0   libsystem_pthread.dylib       	       0x105b65984 start_wqthread + 0

Thread 12:
0   libsystem_pthread.dylib       	       0x105b65984 start_wqthread + 0

Thread 13:
0   libsystem_pthread.dylib       	       0x105b65984 start_wqthread + 0

Thread 14:
0   libsystem_pthread.dylib       	       0x105b65984 start_wqthread + 0

Thread 15:
0   libsystem_pthread.dylib       	       0x105b65984 start_wqthread + 0

Thread 16:
0   libsystem_pthread.dylib       	       0x105b65984 start_wqthread + 0

Thread 17:
0   libsystem_pthread.dylib       	       0x105b65984 start_wqthread + 0

Thread 18:
0   libsystem_pthread.dylib       	       0x105b65984 start_wqthread + 0

Thread 19:
0   libsystem_pthread.dylib       	       0x105b65984 start_wqthread + 0

Thread 20:
0   libsystem_pthread.dylib       	       0x105b65984 start_wqthread + 0

Thread 21:: io.flutter.1.raster
0   libsystem_kernel.dylib        	       0x106324b70 mach_msg2_trap + 8
1   libsystem_kernel.dylib        	       0x10633590c mach_msg2_internal + 72
2   libsystem_kernel.dylib        	       0x10632cc10 mach_msg_overwrite + 480
3   libsystem_kernel.dylib        	       0x106324ee4 mach_msg + 20
4   CoreFoundation                	       0x180428bc4 __CFRunLoopServiceMachPort + 156
5   CoreFoundation                	       0x1804235a4 __CFRunLoopRun + 1148
6   CoreFoundation                	       0x180422cec CFRunLoopRunSpecific + 536
7   Flutter                       	       0x10a4a6438 fml::MessageLoopDarwin::Run() + 88
8   Flutter                       	       0x10a49f804 fml::MessageLoopImpl::DoRun() + 40
9   Flutter                       	       0x10a4a50d0 std::_fl::__function::__func<fml::Thread::Thread(std::_fl::function<void (fml::Thread::ThreadConfig const&)> const&, fml::Thread::ThreadConfig const&)::$_0, std::_fl::allocator<fml::Thread::Thread(std::_fl::function<void (fml::Thread::ThreadConfig const&)> const&, fml::Thread::ThreadConfig const&)::$_0>, void ()>::operator()() + 184
10  Flutter                       	       0x10a4a4da4 fml::ThreadHandle::ThreadHandle(std::_fl::function<void ()>&&)::$_0::__invoke(void*) + 36
11  libsystem_pthread.dylib       	       0x105b6a5f0 _pthread_start + 104
12  libsystem_pthread.dylib       	       0x105b65998 thread_start + 8

Thread 22:: io.flutter.1.io
0   libsystem_kernel.dylib        	       0x106324b70 mach_msg2_trap + 8
1   libsystem_kernel.dylib        	       0x10633590c mach_msg2_internal + 72
2   libsystem_kernel.dylib        	       0x10632cc10 mach_msg_overwrite + 480
3   libsystem_kernel.dylib        	       0x106324ee4 mach_msg + 20
4   CoreFoundation                	       0x180428bc4 __CFRunLoopServiceMachPort + 156
5   CoreFoundation                	       0x1804235a4 __CFRunLoopRun + 1148
6   CoreFoundation                	       0x180422cec CFRunLoopRunSpecific + 536
7   Flutter                       	       0x10a4a6438 fml::MessageLoopDarwin::Run() + 88
8   Flutter                       	       0x10a49f804 fml::MessageLoopImpl::DoRun() + 40
9   Flutter                       	       0x10a4a50d0 std::_fl::__function::__func<fml::Thread::Thread(std::_fl::function<void (fml::Thread::ThreadConfig const&)> const&, fml::Thread::ThreadConfig const&)::$_0, std::_fl::allocator<fml::Thread::Thread(std::_fl::function<void (fml::Thread::ThreadConfig const&)> const&, fml::Thread::ThreadConfig const&)::$_0>, void ()>::operator()() + 184
10  Flutter                       	       0x10a4a4da4 fml::ThreadHandle::ThreadHandle(std::_fl::function<void ()>&&)::$_0::__invoke(void*) + 36
11  libsystem_pthread.dylib       	       0x105b6a5f0 _pthread_start + 104
12  libsystem_pthread.dylib       	       0x105b65998 thread_start + 8

Thread 23:: io.flutter.1.profiler
0   libsystem_kernel.dylib        	       0x106324b70 mach_msg2_trap + 8
1   libsystem_kernel.dylib        	       0x10633590c mach_msg2_internal + 72
2   libsystem_kernel.dylib        	       0x10632cc10 mach_msg_overwrite + 480
3   libsystem_kernel.dylib        	       0x106324ee4 mach_msg + 20
4   CoreFoundation                	       0x180428bc4 __CFRunLoopServiceMachPort + 156
5   CoreFoundation                	       0x1804235a4 __CFRunLoopRun + 1148
6   CoreFoundation                	       0x180422cec CFRunLoopRunSpecific + 536
7   Flutter                       	       0x10a4a6438 fml::MessageLoopDarwin::Run() + 88
8   Flutter                       	       0x10a49f804 fml::MessageLoopImpl::DoRun() + 40
9   Flutter                       	       0x10a4a50d0 std::_fl::__function::__func<fml::Thread::Thread(std::_fl::function<void (fml::Thread::ThreadConfig const&)> const&, fml::Thread::ThreadConfig const&)::$_0, std::_fl::allocator<fml::Thread::Thread(std::_fl::function<void (fml::Thread::ThreadConfig const&)> const&, fml::Thread::ThreadConfig const&)::$_0>, void ()>::operator()() + 184
10  Flutter                       	       0x10a4a4da4 fml::ThreadHandle::ThreadHandle(std::_fl::function<void ()>&&)::$_0::__invoke(void*) + 36
11  libsystem_pthread.dylib       	       0x105b6a5f0 _pthread_start + 104
12  libsystem_pthread.dylib       	       0x105b65998 thread_start + 8

Thread 24:: io.worker.1
0   libsystem_kernel.dylib        	       0x106328020 __psynch_cvwait + 8
1   libsystem_pthread.dylib       	       0x105b6aab8 _pthread_cond_wait + 976
2   Flutter                       	       0x10a47c958 std::_fl::condition_variable::wait(std::_fl::unique_lock<std::_fl::mutex>&) + 24
3   Flutter                       	       0x10a49be34 fml::ConcurrentMessageLoop::WorkerMain() + 140
4   Flutter                       	       0x10a49c760 void* std::_fl::__thread_proxy[abi:nn210000]<std::_fl::tuple<std::_fl::unique_ptr<std::_fl::__thread_struct, std::_fl::default_delete<std::_fl::__thread_struct>>, fml::ConcurrentMessageLoop::ConcurrentMessageLoop(unsigned long)::$_0>>(void*) + 212
5   libsystem_pthread.dylib       	       0x105b6a5f0 _pthread_start + 104
6   libsystem_pthread.dylib       	       0x105b65998 thread_start + 8

Thread 25:: io.worker.2
0   libsystem_kernel.dylib        	       0x106328020 __psynch_cvwait + 8
1   libsystem_pthread.dylib       	       0x105b6aab8 _pthread_cond_wait + 976
2   Flutter                       	       0x10a47c958 std::_fl::condition_variable::wait(std::_fl::unique_lock<std::_fl::mutex>&) + 24
3   Flutter                       	       0x10a49be34 fml::ConcurrentMessageLoop::WorkerMain() + 140
4   Flutter                       	       0x10a49c760 void* std::_fl::__thread_proxy[abi:nn210000]<std::_fl::tuple<std::_fl::unique_ptr<std::_fl::__thread_struct, std::_fl::default_delete<std::_fl::__thread_struct>>, fml::ConcurrentMessageLoop::ConcurrentMessageLoop(unsigned long)::$_0>>(void*) + 212
5   libsystem_pthread.dylib       	       0x105b6a5f0 _pthread_start + 104
6   libsystem_pthread.dylib       	       0x105b65998 thread_start + 8

Thread 26:: io.worker.3
0   libsystem_kernel.dylib        	       0x106328020 __psynch_cvwait + 8
1   libsystem_pthread.dylib       	       0x105b6aab8 _pthread_cond_wait + 976
2   Flutter                       	       0x10a47c958 std::_fl::condition_variable::wait(std::_fl::unique_lock<std::_fl::mutex>&) + 24
3   Flutter                       	       0x10a49be34 fml::ConcurrentMessageLoop::WorkerMain() + 140
4   Flutter                       	       0x10a49c760 void* std::_fl::__thread_proxy[abi:nn210000]<std::_fl::tuple<std::_fl::unique_ptr<std::_fl::__thread_struct, std::_fl::default_delete<std::_fl::__thread_struct>>, fml::ConcurrentMessageLoop::ConcurrentMessageLoop(unsigned long)::$_0>>(void*) + 212
5   libsystem_pthread.dylib       	       0x105b6a5f0 _pthread_start + 104
6   libsystem_pthread.dylib       	       0x105b65998 thread_start + 8

Thread 27:: io.worker.4
0   libsystem_kernel.dylib        	       0x106328020 __psynch_cvwait + 8
1   libsystem_pthread.dylib       	       0x105b6aab8 _pthread_cond_wait + 976
2   Flutter                       	       0x10a47c958 std::_fl::condition_variable::wait(std::_fl::unique_lock<std::_fl::mutex>&) + 24
3   Flutter                       	       0x10a49be34 fml::ConcurrentMessageLoop::WorkerMain() + 140
4   Flutter                       	       0x10a49c760 void* std::_fl::__thread_proxy[abi:nn210000]<std::_fl::tuple<std::_fl::unique_ptr<std::_fl::__thread_struct, std::_fl::default_delete<std::_fl::__thread_struct>>, fml::ConcurrentMessageLoop::ConcurrentMessageLoop(unsigned long)::$_0>>(void*) + 212
5   libsystem_pthread.dylib       	       0x105b6a5f0 _pthread_start + 104
6   libsystem_pthread.dylib       	       0x105b65998 thread_start + 8

Thread 28:: dart:io EventHandler
0   libsystem_kernel.dylib        	       0x10632a65c kevent + 8
1   Flutter                       	       0x10a8e9df8 dart::bin::EventHandlerImplementation::EventHandlerEntry(unsigned long) + 300
2   Flutter                       	       0x10a905f50 dart::bin::ThreadStart(void*) + 88
3   libsystem_pthread.dylib       	       0x105b6a5f0 _pthread_start + 104
4   libsystem_pthread.dylib       	       0x105b65998 thread_start + 8

Thread 29:: DartWorker
0   libsystem_kernel.dylib        	       0x106328020 __psynch_cvwait + 8
1   libsystem_pthread.dylib       	       0x105b6aae4 _pthread_cond_wait + 1020
2   Flutter                       	       0x10a951648 dart::ConditionVariable::WaitMicros(dart::Mutex*, long long) + 112
3   Flutter                       	       0x10aaaf36c dart::ThreadPool::WorkerLoop(dart::ThreadPool::Worker*) + 508
4   Flutter                       	       0x10aaaf4c4 dart::ThreadPool::Worker::Main(unsigned long) + 116
5   Flutter                       	       0x10aa66024 dart::ThreadStart(void*) + 204
6   libsystem_pthread.dylib       	       0x105b6a5f0 _pthread_start + 104
7   libsystem_pthread.dylib       	       0x105b65998 thread_start + 8

Thread 30:: DartWorker
0   libsystem_kernel.dylib        	       0x106328020 __psynch_cvwait + 8
1   libsystem_pthread.dylib       	       0x105b6aae4 _pthread_cond_wait + 1020
2   Flutter                       	       0x10a951648 dart::ConditionVariable::WaitMicros(dart::Mutex*, long long) + 112
3   Flutter                       	       0x10aaaf36c dart::ThreadPool::WorkerLoop(dart::ThreadPool::Worker*) + 508
4   Flutter                       	       0x10aaaf4c4 dart::ThreadPool::Worker::Main(unsigned long) + 116
5   Flutter                       	       0x10aa66024 dart::ThreadStart(void*) + 204
6   libsystem_pthread.dylib       	       0x105b6a5f0 _pthread_start + 104
7   libsystem_pthread.dylib       	       0x105b65998 thread_start + 8

Thread 31:: DartWorker
0   libsystem_kernel.dylib        	       0x106328020 __psynch_cvwait + 8
1   libsystem_pthread.dylib       	       0x105b6aae4 _pthread_cond_wait + 1020
2   Flutter                       	       0x10a951648 dart::ConditionVariable::WaitMicros(dart::Mutex*, long long) + 112
3   Flutter                       	       0x10a9af88c dart::MutatorThreadPool::OnEnterIdleLocked(dart::MutexLocker*, dart::ThreadPool::Worker*) + 152
4   Flutter                       	       0x10aaaf1ec dart::ThreadPool::WorkerLoop(dart::ThreadPool::Worker*) + 124
5   Flutter                       	       0x10aaaf4c4 dart::ThreadPool::Worker::Main(unsigned long) + 116
6   Flutter                       	       0x10aa66024 dart::ThreadStart(void*) + 204
7   libsystem_pthread.dylib       	       0x105b6a5f0 _pthread_start + 104
8   libsystem_pthread.dylib       	       0x105b65998 thread_start + 8

Thread 32:: caulk.messenger.shared:17
0   libsystem_kernel.dylib        	       0x106324aec semaphore_wait_trap + 8
1   caulk                         	       0x1b6b51b3c caulk::semaphore::timed_wait(double) + 220
2   caulk                         	       0x1b6b58c40 caulk::concurrent::details::worker_thread::run() + 28
3   caulk                         	       0x1b6b58cb4 void* caulk::thread_proxy<std::__1::tuple<caulk::thread::attributes, void (caulk::concurrent::details::worker_thread::*)(), std::__1::tuple<caulk::concurrent::details::worker_thread*>>>(void*) + 48
4   libsystem_pthread.dylib       	       0x105b6a5f0 _pthread_start + 104
5   libsystem_pthread.dylib       	       0x105b65998 thread_start + 8

Thread 33:: caulk.messenger.shared:high
0   libsystem_kernel.dylib        	       0x106324aec semaphore_wait_trap + 8
1   caulk                         	       0x1b6b51b3c caulk::semaphore::timed_wait(double) + 220
2   caulk                         	       0x1b6b58c40 caulk::concurrent::details::worker_thread::run() + 28
3   caulk                         	       0x1b6b58cb4 void* caulk::thread_proxy<std::__1::tuple<caulk::thread::attributes, void (caulk::concurrent::details::worker_thread::*)(), std::__1::tuple<caulk::concurrent::details::worker_thread*>>>(void*) + 48
4   libsystem_pthread.dylib       	       0x105b6a5f0 _pthread_start + 104
5   libsystem_pthread.dylib       	       0x105b65998 thread_start + 8

Thread 34:: DartWorker
0   libsystem_kernel.dylib        	       0x106328020 __psynch_cvwait + 8
1   libsystem_pthread.dylib       	       0x105b6aae4 _pthread_cond_wait + 1020
2   Flutter                       	       0x10a951648 dart::ConditionVariable::WaitMicros(dart::Mutex*, long long) + 112
3   Flutter                       	       0x10aaaf36c dart::ThreadPool::WorkerLoop(dart::ThreadPool::Worker*) + 508
4   Flutter                       	       0x10aaaf4c4 dart::ThreadPool::Worker::Main(unsigned long) + 116
5   Flutter                       	       0x10aa66024 dart::ThreadStart(void*) + 204
6   libsystem_pthread.dylib       	       0x105b6a5f0 _pthread_start + 104
7   libsystem_pthread.dylib       	       0x105b65998 thread_start + 8


Thread 0 crashed with ARM Thread State (64-bit):
    x0: 0x0000000000000000   x1: 0x0000000000000000   x2: 0x0000000000000000   x3: 0x0000000000000000
    x4: 0x00000001802b3e9b   x5: 0x000000016b22faf0   x6: 0x000000000000006e   x7: 0x0000000000000000
    x8: 0x0000000104d85e40   x9: 0x482fa7027551597a  x10: 0x0000000000000051  x11: 0x000000000000000b
   x12: 0x000000000000000b  x13: 0x00000001806e8af6  x14: 0x00000000000007fb  x15: 0x000000009a625027
   x16: 0x0000000000000148  x17: 0x000000009a82482a  x18: 0x0000000000000000  x19: 0x0000000000000006
   x20: 0x0000000000000103  x21: 0x0000000104d85f20  x22: 0x00000001f2ca9000  x23: 0x00000001f2ca6ac0
   x24: 0x00000001f2ca6ac0  x25: 0x000060000174b6c0  x26: 0x0000600002c24180  x27: 0x0000000000000000
   x28: 0x0000000000000114   fp: 0x000000016b22fa60   lr: 0x0000000105b6a2ec
    sp: 0x000000016b22fa40   pc: 0x000000010632c85c cpsr: 0x40001000
   far: 0x0000000000000000  esr: 0x56000080 (PC alignment)

Binary Images:
       0x104cd8000 -        0x104d77fff dyld (*) <ff357121-622e-3ea0-9ef0-8d706f44dc1a> /usr/lib/dyld
       0x104bcc000 -        0x104bcffff com.hopenapp.hopen (1.0.0) <caf7731a-2c8a-3262-ac04-f72f06f73eab> /Users/<USER>/Library/Developer/CoreSimulator/Devices/24942F86-3060-4B2A-962B-60BD9614F1F4/data/Containers/Bundle/Application/13FF1FEF-7257-49BB-8287-1FCD0810CD0E/Runner.app/Runner
       0x104f08000 -        0x104f8bfff Runner.debug.dylib (*) <c14c9eb6-9864-3bfd-b8df-604b8f776e52> /Users/<USER>/Library/Developer/CoreSimulator/Devices/24942F86-3060-4B2A-962B-60BD9614F1F4/data/Containers/Bundle/Application/13FF1FEF-7257-49BB-8287-1FCD0810CD0E/Runner.app/Runner.debug.dylib
       0x104e68000 -        0x104e8ffff org.cocoapods.AppAuth (1.7.6) <cd329467-4e64-33fd-8f81-c140b9c607a9> /Users/<USER>/Library/Developer/CoreSimulator/Devices/24942F86-3060-4B2A-962B-60BD9614F1F4/data/Containers/Bundle/Application/13FF1FEF-7257-49BB-8287-1FCD0810CD0E/Runner.app/Frameworks/AppAuth.framework/AppAuth
       0x105004000 -        0x105027fff org.cocoapods.AppCheckCore (11.2.0) <31ce7028-5a68-39f1-a840-891714831d69> /Users/<USER>/Library/Developer/CoreSimulator/Devices/24942F86-3060-4B2A-962B-60BD9614F1F4/data/Containers/Bundle/Application/13FF1FEF-7257-49BB-8287-1FCD0810CD0E/Runner.app/Frameworks/AppCheckCore.framework/AppCheckCore
       0x1051dc000 -        0x105277fff org.cocoapods.DKImagePickerController (4.3.9) <03fcf312-d00e-3900-9646-92f66cbdaf51> /Users/<USER>/Library/Developer/CoreSimulator/Devices/24942F86-3060-4B2A-962B-60BD9614F1F4/data/Containers/Bundle/Application/13FF1FEF-7257-49BB-8287-1FCD0810CD0E/Runner.app/Frameworks/DKImagePickerController.framework/DKImagePickerController
       0x10535c000 -        0x1053e3fff org.cocoapods.DKPhotoGallery (0.0.19) <9cc3f835-0645-3b4c-962e-78404b6674c6> /Users/<USER>/Library/Developer/CoreSimulator/Devices/24942F86-3060-4B2A-962B-60BD9614F1F4/data/Containers/Bundle/Application/13FF1FEF-7257-49BB-8287-1FCD0810CD0E/Runner.app/Frameworks/DKPhotoGallery.framework/DKPhotoGallery
       0x104e0c000 -        0x104e1ffff org.cocoapods.FBLPromises (2.4.0) <962c6d34-cde2-3ecf-ba9b-3af78f34ab5a> /Users/<USER>/Library/Developer/CoreSimulator/Devices/24942F86-3060-4B2A-962B-60BD9614F1F4/data/Containers/Bundle/Application/13FF1FEF-7257-49BB-8287-1FCD0810CD0E/Runner.app/Frameworks/FBLPromises.framework/FBLPromises
       0x104ec0000 -        0x104ed3fff org.cocoapods.FirebaseCore (11.15.0) <5e0d33ee-5834-3d02-972b-27a987c049e3> /Users/<USER>/Library/Developer/CoreSimulator/Devices/24942F86-3060-4B2A-962B-60BD9614F1F4/data/Containers/Bundle/Application/13FF1FEF-7257-49BB-8287-1FCD0810CD0E/Runner.app/Frameworks/FirebaseCore.framework/FirebaseCore
       0x1050d8000 -        0x1050fffff org.cocoapods.FirebaseCoreInternal (11.15.0) <29efc00f-307c-3f23-80fa-534b89bd3c78> /Users/<USER>/Library/Developer/CoreSimulator/Devices/24942F86-3060-4B2A-962B-60BD9614F1F4/data/Containers/Bundle/Application/13FF1FEF-7257-49BB-8287-1FCD0810CD0E/Runner.app/Frameworks/FirebaseCoreInternal.framework/FirebaseCoreInternal
       0x105150000 -        0x10516bfff org.cocoapods.FirebaseInstallations (11.15.0) <d9fbd69a-5b18-38a7-adc4-499fc338c58c> /Users/<USER>/Library/Developer/CoreSimulator/Devices/24942F86-3060-4B2A-962B-60BD9614F1F4/data/Containers/Bundle/Application/13FF1FEF-7257-49BB-8287-1FCD0810CD0E/Runner.app/Frameworks/FirebaseInstallations.framework/FirebaseInstallations
       0x10552c000 -        0x105573fff org.cocoapods.FirebaseMessaging (11.15.0) <2decb982-fd1b-3698-ade5-50d8b05739d2> /Users/<USER>/Library/Developer/CoreSimulator/Devices/24942F86-3060-4B2A-962B-60BD9614F1F4/data/Containers/Bundle/Application/13FF1FEF-7257-49BB-8287-1FCD0810CD0E/Runner.app/Frameworks/FirebaseMessaging.framework/FirebaseMessaging
       0x105498000 -        0x1054bbfff org.cocoapods.GTMAppAuth (4.1.1) <b56e79ea-28fb-3a59-b431-e8fc09b71b48> /Users/<USER>/Library/Developer/CoreSimulator/Devices/24942F86-3060-4B2A-962B-60BD9614F1F4/data/Containers/Bundle/Application/13FF1FEF-7257-49BB-8287-1FCD0810CD0E/Runner.app/Frameworks/GTMAppAuth.framework/GTMAppAuth
       0x10565c000 -        0x1056b3fff org.cocoapods.GTMSessionFetcher (3.5.0) <205fdd37-40d9-38fe-b575-614ac31c7140> /Users/<USER>/Library/Developer/CoreSimulator/Devices/24942F86-3060-4B2A-962B-60BD9614F1F4/data/Containers/Bundle/Application/13FF1FEF-7257-49BB-8287-1FCD0810CD0E/Runner.app/Frameworks/GTMSessionFetcher.framework/GTMSessionFetcher
       0x1055bc000 -        0x1055effff org.cocoapods.GoogleDataTransport (10.1.0) <a5c335de-684e-3b83-ba64-e21e447ea889> /Users/<USER>/Library/Developer/CoreSimulator/Devices/24942F86-3060-4B2A-962B-60BD9614F1F4/data/Containers/Bundle/Application/13FF1FEF-7257-49BB-8287-1FCD0810CD0E/Runner.app/Frameworks/GoogleDataTransport.framework/GoogleDataTransport
       0x1056f8000 -        0x10571bfff org.cocoapods.GoogleSignIn (8.0.0) <55fb2deb-d907-305e-a508-ee748f35ff18> /Users/<USER>/Library/Developer/CoreSimulator/Devices/24942F86-3060-4B2A-962B-60BD9614F1F4/data/Containers/Bundle/Application/13FF1FEF-7257-49BB-8287-1FCD0810CD0E/Runner.app/Frameworks/GoogleSignIn.framework/GoogleSignIn
       0x10574c000 -        0x10576ffff org.cocoapods.GoogleUtilities (8.1.0) <579d30f5-873d-3cac-afaa-2ef68808afd3> /Users/<USER>/Library/Developer/CoreSimulator/Devices/24942F86-3060-4B2A-962B-60BD9614F1F4/data/Containers/Bundle/Application/13FF1FEF-7257-49BB-8287-1FCD0810CD0E/Runner.app/Frameworks/GoogleUtilities.framework/GoogleUtilities
       0x104ca0000 -        0x104cbbfff org.cocoapods.Mantle (2.2.0) <6b831635-7679-349a-8447-49703eebba25> /Users/<USER>/Library/Developer/CoreSimulator/Devices/24942F86-3060-4B2A-962B-60BD9614F1F4/data/Containers/Bundle/Application/13FF1FEF-7257-49BB-8287-1FCD0810CD0E/Runner.app/Frameworks/Mantle.framework/Mantle
       0x105c88000 -        0x105ebbfff org.cocoapods.PostHog (3.29.0) <780581b9-7198-311c-820d-77468c8d52c9> /Users/<USER>/Library/Developer/CoreSimulator/Devices/24942F86-3060-4B2A-962B-60BD9614F1F4/data/Containers/Bundle/Application/13FF1FEF-7257-49BB-8287-1FCD0810CD0E/Runner.app/Frameworks/PostHog.framework/PostHog
       0x105090000 -        0x10509bfff org.cocoapods.Reachability (5.2.4) <d5570c0a-e256-3e20-8732-c2b425cc2bce> /Users/<USER>/Library/Developer/CoreSimulator/Devices/24942F86-3060-4B2A-962B-60BD9614F1F4/data/Containers/Bundle/Application/13FF1FEF-7257-49BB-8287-1FCD0810CD0E/Runner.app/Frameworks/Reachability.framework/Reachability
       0x105898000 -        0x105917fff org.cocoapods.SDWebImage (5.21.1) <43368b18-84f3-3020-bd85-9da338c90e8a> /Users/<USER>/Library/Developer/CoreSimulator/Devices/24942F86-3060-4B2A-962B-60BD9614F1F4/data/Containers/Bundle/Application/13FF1FEF-7257-49BB-8287-1FCD0810CD0E/Runner.app/Frameworks/SDWebImage.framework/SDWebImage
       0x10505c000 -        0x105067fff org.cocoapods.SDWebImageWebPCoder (0.14.6) <070de3a9-68cf-36c8-965a-e06b41c095bc> /Users/<USER>/Library/Developer/CoreSimulator/Devices/24942F86-3060-4B2A-962B-60BD9614F1F4/data/Containers/Bundle/Application/13FF1FEF-7257-49BB-8287-1FCD0810CD0E/Runner.app/Frameworks/SDWebImageWebPCoder.framework/SDWebImageWebPCoder
       0x105798000 -        0x1057affff org.cocoapods.SwiftyGif (5.4.5) <88cedd7c-e176-32e9-aaa4-99c7c8e78da9> /Users/<USER>/Library/Developer/CoreSimulator/Devices/24942F86-3060-4B2A-962B-60BD9614F1F4/data/Containers/Bundle/Application/13FF1FEF-7257-49BB-8287-1FCD0810CD0E/Runner.app/Frameworks/SwiftyGif.framework/SwiftyGif
       0x106b90000 -        0x1074c7fff org.webrtc.WebRTC (1.0) <4c4c4404-5555-3144-a1e8-358e2ad27d7b> /Users/<USER>/Library/Developer/CoreSimulator/Devices/24942F86-3060-4B2A-962B-60BD9614F1F4/data/Containers/Bundle/Application/13FF1FEF-7257-49BB-8287-1FCD0810CD0E/Runner.app/Frameworks/WebRTC.framework/WebRTC
       0x105838000 -        0x105857fff org.cocoapods.audioplayers-darwin (0.0.1) <5dba94ce-5e49-3df4-a5dc-1b20006850a3> /Users/<USER>/Library/Developer/CoreSimulator/Devices/24942F86-3060-4B2A-962B-60BD9614F1F4/data/Containers/Bundle/Application/13FF1FEF-7257-49BB-8287-1FCD0810CD0E/Runner.app/Frameworks/audioplayers_darwin.framework/audioplayers_darwin
       0x105194000 -        0x10519ffff org.cocoapods.background-fetch (1.3.8) <3082c5bb-a16c-31e6-8582-dcdb7654da58> /Users/<USER>/Library/Developer/CoreSimulator/Devices/24942F86-3060-4B2A-962B-60BD9614F1F4/data/Containers/Bundle/Application/13FF1FEF-7257-49BB-8287-1FCD0810CD0E/Runner.app/Frameworks/background_fetch.framework/background_fetch
       0x105a4c000 -        0x105a9bfff org.cocoapods.camera-avfoundation (0.0.1) <57779bd3-e5f9-39e2-8a94-6cc8e13eb4db> /Users/<USER>/Library/Developer/CoreSimulator/Devices/24942F86-3060-4B2A-962B-60BD9614F1F4/data/Containers/Bundle/Application/13FF1FEF-7257-49BB-8287-1FCD0810CD0E/Runner.app/Frameworks/camera_avfoundation.framework/camera_avfoundation
       0x105628000 -        0x105633fff org.cocoapods.connectivity-plus (0.0.1) <a9267679-a863-3c9c-86b8-421fa69f8fdd> /Users/<USER>/Library/Developer/CoreSimulator/Devices/24942F86-3060-4B2A-962B-60BD9614F1F4/data/Containers/Bundle/Application/13FF1FEF-7257-49BB-8287-1FCD0810CD0E/Runner.app/Frameworks/connectivity_plus.framework/connectivity_plus
       0x104e40000 -        0x104e4ffff org.cocoapods.cupertino-http (0.0.1) <c534a179-fcdb-3f80-b7d3-00ce32507584> /Users/<USER>/Library/Developer/CoreSimulator/Devices/24942F86-3060-4B2A-962B-60BD9614F1F4/data/Containers/Bundle/Application/13FF1FEF-7257-49BB-8287-1FCD0810CD0E/Runner.app/Frameworks/cupertino_http.framework/cupertino_http
       0x1057dc000 -        0x1057effff org.cocoapods.file-picker (0.0.1) <c0502278-71ed-3e27-ade8-7c16e317672e> /Users/<USER>/Library/Developer/CoreSimulator/Devices/24942F86-3060-4B2A-962B-60BD9614F1F4/data/Containers/Bundle/Application/13FF1FEF-7257-49BB-8287-1FCD0810CD0E/Runner.app/Frameworks/file_picker.framework/file_picker
       0x105b00000 -        0x105b23fff org.cocoapods.flutter-image-compress-common (1.0.0) <e0b45e4d-d6f6-3269-ba72-505c1df8f371> /Users/<USER>/Library/Developer/CoreSimulator/Devices/24942F86-3060-4B2A-962B-60BD9614F1F4/data/Containers/Bundle/Application/13FF1FEF-7257-49BB-8287-1FCD0810CD0E/Runner.app/Frameworks/flutter_image_compress_common.framework/flutter_image_compress_common
       0x105808000 -        0x105817fff org.cocoapods.flutter-local-notifications (0.0.1) <a3de282d-4d89-34ed-b74c-6a0c45a157aa> /Users/<USER>/Library/Developer/CoreSimulator/Devices/24942F86-3060-4B2A-962B-60BD9614F1F4/data/Containers/Bundle/Application/13FF1FEF-7257-49BB-8287-1FCD0810CD0E/Runner.app/Frameworks/flutter_local_notifications.framework/flutter_local_notifications
       0x105994000 -        0x1059a3fff org.cocoapods.flutter-secure-storage (6.0.0) <d709d26f-6cb7-3452-91d8-5ab0d96da42e> /Users/<USER>/Library/Developer/CoreSimulator/Devices/24942F86-3060-4B2A-962B-60BD9614F1F4/data/Containers/Bundle/Application/13FF1FEF-7257-49BB-8287-1FCD0810CD0E/Runner.app/Frameworks/flutter_secure_storage.framework/flutter_secure_storage
       0x1059c4000 -        0x1059d7fff org.cocoapods.image-picker-ios (0.0.1) <d8c14994-b67e-3089-ad07-ef19b43fdc0b> /Users/<USER>/Library/Developer/CoreSimulator/Devices/24942F86-3060-4B2A-962B-60BD9614F1F4/data/Containers/Bundle/Application/13FF1FEF-7257-49BB-8287-1FCD0810CD0E/Runner.app/Frameworks/image_picker_ios.framework/image_picker_ios
       0x106180000 -        0x10621ffff org.cocoapods.libwebp (1.5.0) <ac0f2aff-6f25-3923-951f-5204e2d38d98> /Users/<USER>/Library/Developer/CoreSimulator/Devices/24942F86-3060-4B2A-962B-60BD9614F1F4/data/Containers/Bundle/Application/13FF1FEF-7257-49BB-8287-1FCD0810CD0E/Runner.app/Frameworks/libwebp.framework/libwebp
       0x1050c0000 -        0x1050c7fff org.cocoapods.nanopb (3.30910.0) <6762a519-8c26-374b-9732-572f22a294a8> /Users/<USER>/Library/Developer/CoreSimulator/Devices/24942F86-3060-4B2A-962B-60BD9614F1F4/data/Containers/Bundle/Application/13FF1FEF-7257-49BB-8287-1FCD0810CD0E/Runner.app/Frameworks/nanopb.framework/nanopb
       0x1054f4000 -        0x1054fbfff org.cocoapods.objective-c (0.0.1) <0d8a2c56-fdbb-3c89-803a-4f866892c0b2> /Users/<USER>/Library/Developer/CoreSimulator/Devices/24942F86-3060-4B2A-962B-60BD9614F1F4/data/Containers/Bundle/Application/13FF1FEF-7257-49BB-8287-1FCD0810CD0E/Runner.app/Frameworks/objective_c.framework/objective_c
       0x105a24000 -        0x105a2ffff org.cocoapods.path-provider-foundation (0.0.1) <ea818c3f-c528-3db6-9e33-cc9232a2234a> /Users/<USER>/Library/Developer/CoreSimulator/Devices/24942F86-3060-4B2A-962B-60BD9614F1F4/data/Containers/Bundle/Application/13FF1FEF-7257-49BB-8287-1FCD0810CD0E/Runner.app/Frameworks/path_provider_foundation.framework/path_provider_foundation
       0x1059f8000 -        0x105a07fff org.cocoapods.posthog-flutter (0.0.1) <05eb2d96-d712-30ef-abfb-0c9e0bd2e106> /Users/<USER>/Library/Developer/CoreSimulator/Devices/24942F86-3060-4B2A-962B-60BD9614F1F4/data/Containers/Bundle/Application/13FF1FEF-7257-49BB-8287-1FCD0810CD0E/Runner.app/Frameworks/posthog_flutter.framework/posthog_flutter
       0x105bc4000 -        0x105be7fff org.cocoapods.record-ios (1.0.0) <4fb8fc2b-1094-37ca-aa9a-c48d9937c6de> /Users/<USER>/Library/Developer/CoreSimulator/Devices/24942F86-3060-4B2A-962B-60BD9614F1F4/data/Containers/Bundle/Application/13FF1FEF-7257-49BB-8287-1FCD0810CD0E/Runner.app/Frameworks/record_ios.framework/record_ios
       0x105c20000 -        0x105c33fff org.cocoapods.shared-preferences-foundation (0.0.1) <7f787c28-8ab8-38d1-9f4a-9c0acc03d294> /Users/<USER>/Library/Developer/CoreSimulator/Devices/24942F86-3060-4B2A-962B-60BD9614F1F4/data/Containers/Bundle/Application/13FF1FEF-7257-49BB-8287-1FCD0810CD0E/Runner.app/Frameworks/shared_preferences_foundation.framework/shared_preferences_foundation
       0x10626c000 -        0x10627ffff org.cocoapods.sign-in-with-apple (0.0.1) <6a97bc70-a8c9-3481-bc7f-0c94463d6fc7> /Users/<USER>/Library/Developer/CoreSimulator/Devices/24942F86-3060-4B2A-962B-60BD9614F1F4/data/Containers/Bundle/Application/13FF1FEF-7257-49BB-8287-1FCD0810CD0E/Runner.app/Frameworks/sign_in_with_apple.framework/sign_in_with_apple
       0x1062a4000 -        0x1062c3fff org.cocoapods.sqflite-darwin (0.0.4) <8089bff1-2204-3398-b695-4d26b27ed341> /Users/<USER>/Library/Developer/CoreSimulator/Devices/24942F86-3060-4B2A-962B-60BD9614F1F4/data/Containers/Bundle/Application/13FF1FEF-7257-49BB-8287-1FCD0810CD0E/Runner.app/Frameworks/sqflite_darwin.framework/sqflite_darwin
       0x1062ec000 -        0x106303fff org.cocoapods.video-player-avfoundation (0.0.1) <c69d2e76-b993-3733-b43c-444bb697ef2b> /Users/<USER>/Library/Developer/CoreSimulator/Devices/24942F86-3060-4B2A-962B-60BD9614F1F4/data/Containers/Bundle/Application/13FF1FEF-7257-49BB-8287-1FCD0810CD0E/Runner.app/Frameworks/video_player_avfoundation.framework/video_player_avfoundation
       0x1064d4000 -        0x10657ffff org.cocoapods.webview-flutter-wkwebview (0.0.1) <ec39c002-5a1d-3a12-a28b-6f9ab7cfcbb4> /Users/<USER>/Library/Developer/CoreSimulator/Devices/24942F86-3060-4B2A-962B-60BD9614F1F4/data/Containers/Bundle/Application/13FF1FEF-7257-49BB-8287-1FCD0810CD0E/Runner.app/Frameworks/webview_flutter_wkwebview.framework/webview_flutter_wkwebview
       0x106398000 -        0x1063bffff org.cocoapods.workmanager (0.0.1) <f1b859ba-2861-3133-a066-20a0754936f2> /Users/<USER>/Library/Developer/CoreSimulator/Devices/24942F86-3060-4B2A-962B-60BD9614F1F4/data/Containers/Bundle/Application/13FF1FEF-7257-49BB-8287-1FCD0810CD0E/Runner.app/Frameworks/workmanager.framework/workmanager
       0x10a418000 -        0x10c593fff io.flutter.flutter (1.0) <4c4c4445-5555-3144-a11f-921ceb984ad8> /Users/<USER>/Library/Developer/CoreSimulator/Devices/24942F86-3060-4B2A-962B-60BD9614F1F4/data/Containers/Bundle/Application/13FF1FEF-7257-49BB-8287-1FCD0810CD0E/Runner.app/Frameworks/Flutter.framework/Flutter
       0x1051b8000 -        0x1051bffff libsystem_platform.dylib (*) <f77d8041-529c-3659-ac12-4821c1c301fc> /usr/lib/system/libsystem_platform.dylib
       0x106324000 -        0x10635ffff libsystem_kernel.dylib (*) <f30ad29e-a6a0-3f43-8f6e-3770f040794d> /usr/lib/system/libsystem_kernel.dylib
       0x105b64000 -        0x105b73fff libsystem_pthread.dylib (*) <7d5c0390-4e96-3458-ad9c-5c41b02beefd> /usr/lib/system/libsystem_pthread.dylib
       0x106900000 -        0x10690bfff libobjc-trampolines.dylib (*) <56878cbd-4b61-3d67-a830-23a1b2beaf59> /Volumes/VOLUME/*/libobjc-trampolines.dylib
       0x1800fe000 -        0x18017a79b libsystem_c.dylib (*) <d8eab09a-74cb-346d-b14e-3187db1c40db> /Volumes/VOLUME/*/libsystem_c.dylib
       0x18029b000 -        0x1802b6fff libc++abi.dylib (*) <649076f2-9c2b-3e8e-833b-d246ac43869e> /Volumes/VOLUME/*/libc++abi.dylib
       0x180068000 -        0x1800a491f libobjc.A.dylib (*) <b10e226d-4fab-3450-9a4c-071d3d0edf3a> /Volumes/VOLUME/*/libobjc.A.dylib
       0x180396000 -        0x1807adfff com.apple.CoreFoundation (6.9) <ae27f481-c1fa-359c-b04c-af9cda7655ff> /Volumes/VOLUME/*/CoreFoundation.framework/CoreFoundation
       0x18017b000 -        0x1801bfb9f libdispatch.dylib (*) <990151a6-fd18-3496-84e3-f565307fbc2f> /Volumes/VOLUME/*/libdispatch.dylib
       0x191002000 -        0x19100a1ff com.apple.GraphicsServices (1.0) <80b30bb2-e6e1-317e-b798-ea590de713a8> /Volumes/VOLUME/*/GraphicsServices.framework/GraphicsServices
       0x184dfb000 -        0x186b672bf com.apple.UIKitCore (1.0) <f5406608-aa34-30ba-8494-0a8b531792f5> /Volumes/VOLUME/*/UIKitCore.framework/UIKitCore
               0x0 - 0xffffffffffffffff ??? (*) <00000000-0000-0000-0000-000000000000> ???
       0x18082d000 -        0x1813dce3f com.apple.Foundation (6.9) <48eb0271-c8d1-359a-bd56-bcf3e7e37dc5> /Volumes/VOLUME/*/Foundation.framework/Foundation
       0x1b6b42000 -        0x1b6b675df com.apple.audio.caulk (1.0) <7f22c3c4-1a93-34dd-bf46-522f8d2a0e77> /Volumes/VOLUME/*/caulk.framework/caulk

External Modification Summary:
  Calls made by other processes targeting this process:
    task_for_pid: 0
    thread_create: 0
    thread_set_state: 0
  Calls made by this process:
    task_for_pid: 0
    thread_create: 0
    thread_set_state: 0
  Calls made by all processes on this machine:
    task_for_pid: 0
    thread_create: 0
    thread_set_state: 0

VM Region Summary:
ReadOnly portion of Libraries: Total=1.6G resident=0K(0%) swapped_out_or_unallocated=1.6G(100%)
Writable regions: Total=1.7G written=2056K(0%) resident=2056K(0%) swapped_out=0K(0%) unallocated=1.7G(100%)

                                VIRTUAL   REGION 
REGION TYPE                        SIZE    COUNT (non-coalesced) 
===========                     =======  ======= 
Activity Tracing                   256K        1 
ColorSync                           64K        4 
Foundation                          16K        1 
IOSurface                           16K        1 
Kernel Alloc Once                   32K        1 
MALLOC                             1.6G       48 
MALLOC guard page                  192K       12 
STACK GUARD                       56.5M       35 
Stack                             33.0M       35 
VM_ALLOCATE                      110.4M      312 
__DATA                            33.3M      821 
__DATA_CONST                      94.8M      839 
__DATA_DIRTY                       139K       11 
__FONT_DATA                        2352        1 
__LINKEDIT                       709.8M       52 
__OBJC_RO                         61.3M        1 
__OBJC_RW                         2727K        1 
__TEXT                           934.4M      854 
__TPRO_CONST                       148K        2 
dyld private memory                2.5G       14 
mapped file                      138.1M       17 
page table in kernel              2056K        1 
shared memory                       48K        3 
===========                     =======  ======= 
TOTAL                              6.2G     3067 


-----------
Full Report
-----------

{"app_name":"Runner","timestamp":"2025-07-19 22:34:22.00 -0400","app_version":"1.0.0","slice_uuid":"caf7731a-2c8a-3262-ac04-f72f06f73eab","build_version":"1","platform":7,"bundleID":"com.hopenapp.hopen","share_with_app_devs":0,"is_first_party":0,"bug_type":"309","os_version":"macOS 26.0 (25A5306g)","roots_installed":0,"name":"Runner","incident_id":"BD82B5BE-4845-4E46-8B89-8EC915C7A70F"}
{
  "uptime" : 140000,
  "procRole" : "Foreground",
  "version" : 2,
  "userID" : 501,
  "deployVersion" : 210,
  "modelCode" : "MacBookPro18,3",
  "coalitionID" : 103843,
  "osVersion" : {
    "train" : "macOS 26.0",
    "build" : "25A5306g",
    "releaseType" : "User"
  },
  "captureTime" : "2025-07-19 22:34:18.1839 -0400",
  "codeSigningMonitor" : 1,
  "incident" : "BD82B5BE-4845-4E46-8B89-8EC915C7A70F",
  "pid" : 91929,
  "translated" : false,
  "cpuType" : "ARM-64",
  "roots_installed" : 0,
  "bug_type" : "309",
  "procLaunch" : "2025-07-19 22:34:14.5923 -0400",
  "procStartAbsTime" : 3468254504548,
  "procExitAbsTime" : 3468340380078,
  "procName" : "Runner",
  "procPath" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/24942F86-3060-4B2A-962B-60BD9614F1F4\/data\/Containers\/Bundle\/Application\/13FF1FEF-7257-49BB-8287-1FCD0810CD0E\/Runner.app\/Runner",
  "bundleInfo" : {"CFBundleShortVersionString":"1.0.0","CFBundleVersion":"1","CFBundleIdentifier":"com.hopenapp.hopen"},
  "storeInfo" : {"deviceIdentifierForVendor":"6DD9F130-DC4B-5473-B2C5-D45547FA8B3F","thirdParty":true},
  "parentProc" : "launchd_sim",
  "parentPid" : 68304,
  "coalitionName" : "com.apple.CoreSimulator.SimDevice.24942F86-3060-4B2A-962B-60BD9614F1F4",
  "crashReporterKey" : "65553204-4A28-33BB-B7F3-8A48D7F2DE89",
  "developerMode" : 1,
  "responsiblePid" : 12609,
  "responsibleProc" : "SimulatorTrampoline",
  "codeSigningID" : "com.hopenapp.hopen",
  "codeSigningTeamID" : "",
  "codeSigningFlags" : 570425857,
  "codeSigningValidationCategory" : 10,
  "codeSigningTrustLevel" : 4294967295,
  "codeSigningAuxiliaryInfo" : 0,
  "instructionByteStream" : {"beforePC":"4wAAVP17v6n9AwCRKeP\/l78DAJH9e8GowANf1sADX9YQKYDSARAA1A==","atPC":"4wAAVP17v6n9AwCRH+P\/l78DAJH9e8GowANf1sADX9ZwCoDSARAA1A=="},
  "bootSessionUUID" : "F1193B8E-9C88-43F9-978B-736CB73CED37",
  "wakeTime" : 43291,
  "sleepWakeUUID" : "3E9C9FEE-CE70-435F-BB7B-7D695E48AC2B",
  "sip" : "enabled",
  "exception" : {"codes":"0x0000000000000000, 0x0000000000000000","rawCodes":[0,0],"type":"EXC_CRASH","signal":"SIGABRT"},
  "termination" : {"flags":0,"code":6,"namespace":"SIGNAL","indicator":"Abort trap: 6","byProc":"Runner","byPid":91929},
  "extMods" : {"caller":{"thread_create":0,"thread_set_state":0,"task_for_pid":0},"system":{"thread_create":0,"thread_set_state":0,"task_for_pid":0},"targeted":{"thread_create":0,"thread_set_state":0,"task_for_pid":0},"warnings":0},
  "lastExceptionBacktrace" : [{"imageOffset":1259464,"symbol":"__exceptionPreprocess","symbolLocation":160,"imageIndex":54},{"imageOffset":178124,"symbol":"objc_exception_throw","symbolLocation":72,"imageIndex":53},{"imageOffset":1259236,"symbol":"-[NSException initWithCoder:]","symbolLocation":0,"imageIndex":54},{"imageOffset":4908,"sourceLine":115,"sourceFile":"FIRApp.m","symbol":"+[FIRApp configure]","imageIndex":8,"symbolLocation":120},{"imageOffset":19456,"sourceLine":49,"sourceFile":"AppDelegate.swift","symbol":"closure #2 in AppDelegate.application(_:didFinishLaunchingWithOptions:)","imageIndex":2,"symbolLocation":148},{"imageOffset":20252,"sourceFile":"\/<compiler-generated>","symbol":"thunk for @escaping @callee_guaranteed () -> ()","symbolLocation":48,"imageIndex":2},{"imageOffset":6024,"symbol":"_dispatch_call_block_and_release","symbolLocation":24,"imageIndex":55},{"imageOffset":115320,"symbol":"_dispatch_client_callout","symbolLocation":12,"imageIndex":55},{"imageOffset":229388,"symbol":"<deduplicated_symbol>","symbolLocation":24,"imageIndex":55},{"imageOffset":70084,"symbol":"_dispatch_main_queue_drain","symbolLocation":1184,"imageIndex":55},{"imageOffset":68884,"symbol":"_dispatch_main_queue_callback_4CF","symbolLocation":40,"imageIndex":55},{"imageOffset":601756,"symbol":"__CFRUNLOOP_IS_SERVICING_THE_MAIN_DISPATCH_QUEUE__","symbolLocation":12,"imageIndex":54},{"imageOffset":579752,"symbol":"__CFRunLoopRun","symbolLocation":1920,"imageIndex":54},{"imageOffset":576748,"symbol":"CFRunLoopRunSpecific","symbolLocation":536,"imageIndex":54},{"imageOffset":11520,"symbol":"GSEventRunModal","symbolLocation":164,"imageIndex":56},{"imageOffset":15067092,"symbol":"-[UIApplication _run]","symbolLocation":796,"imageIndex":57},{"imageOffset":15084448,"symbol":"UIApplicationMain","symbolLocation":124,"imageIndex":57},{"imageOffset":2264860,"imageIndex":57},{"imageOffset":49128,"sourceFile":"\/<compiler-generated>","symbol":"static UIApplicationDelegate.main()","symbolLocation":128,"imageIndex":2},{"imageOffset":48984,"sourceFile":"\/<compiler-generated>","symbol":"static AppDelegate.$main()","symbolLocation":44,"imageIndex":2},{"imageOffset":51388,"sourceFile":"\/<compiler-generated>","symbol":"__debug_main_executable_dylib_entry_point","symbolLocation":28,"imageIndex":2},{"imageOffset":4374549460,"imageIndex":58},{"imageOffset":35108,"symbol":"start","symbolLocation":6400,"imageIndex":0}],
  "faultingThread" : 0,
  "threads" : [{"triggered":true,"id":5990456,"threadState":{"x":[{"value":0},{"value":0},{"value":0},{"value":0},{"value":6445285019},{"value":6092421872},{"value":110},{"value":0},{"value":4376256064,"symbolLocation":0,"symbol":"_main_thread"},{"value":5201559723636251002},{"value":81},{"value":11},{"value":11},{"value":6449695478},{"value":2043},{"value":2590134311},{"value":328},{"value":2592229418},{"value":0},{"value":6},{"value":259},{"value":4376256288,"symbolLocation":224,"symbol":"_main_thread"},{"value":8368328704,"symbolLocation":1712,"symbol":"objc_debug_taggedpointer_ext_classes"},{"value":8368319168,"symbolLocation":0,"symbol":"_dispatch_main_q"},{"value":8368319168,"symbolLocation":0,"symbol":"_dispatch_main_q"},{"value":105553140692672},{"value":105553162551680},{"value":0},{"value":276}],"flavor":"ARM_THREAD_STATE64","lr":{"value":4390822636},"cpsr":{"value":1073745920},"fp":{"value":6092421728},"sp":{"value":6092421696},"esr":{"value":1442840704,"description":"(PC alignment)"},"pc":{"value":4398958684,"matchesCrashFrame":1},"far":{"value":0}},"frames":[{"imageOffset":34908,"symbol":"__pthread_kill","symbolLocation":8,"imageIndex":48},{"imageOffset":25324,"symbol":"pthread_kill","symbolLocation":264,"imageIndex":49},{"imageOffset":474792,"symbol":"abort","symbolLocation":100,"imageIndex":51},{"imageOffset":86340,"symbol":"abort_message","symbolLocation":128,"imageIndex":52},{"imageOffset":20044,"symbol":"demangling_terminate_handler()","symbolLocation":296,"imageIndex":52},{"imageOffset":29216,"symbol":"_objc_terminate()","symbolLocation":124,"imageIndex":53},{"imageOffset":83312,"symbol":"std::__terminate(void (*)())","symbolLocation":12,"imageIndex":52},{"imageOffset":95384,"symbol":"__cxxabiv1::failed_throw(__cxxabiv1::__cxa_exception*)","symbolLocation":32,"imageIndex":52},{"imageOffset":95352,"symbol":"__cxa_throw","symbolLocation":88,"imageIndex":52},{"imageOffset":178436,"symbol":"objc_exception_throw","symbolLocation":384,"imageIndex":53},{"imageOffset":1259236,"symbol":"+[NSException raise:format:]","symbolLocation":124,"imageIndex":54},{"imageOffset":4908,"sourceLine":110,"sourceFile":"FIRApp.m","symbol":"+[FIRApp configure]","imageIndex":8,"symbolLocation":120},{"imageOffset":19456,"sourceLine":49,"sourceFile":"AppDelegate.swift","symbol":"closure #2 in AppDelegate.application(_:didFinishLaunchingWithOptions:)","imageIndex":2,"symbolLocation":148},{"imageOffset":20252,"sourceFile":"\/<compiler-generated>","symbol":"thunk for @escaping @callee_guaranteed () -> ()","symbolLocation":48,"imageIndex":2},{"imageOffset":6024,"symbol":"_dispatch_call_block_and_release","symbolLocation":24,"imageIndex":55},{"imageOffset":115320,"symbol":"_dispatch_client_callout","symbolLocation":12,"imageIndex":55},{"imageOffset":229388,"symbol":"<deduplicated_symbol>","symbolLocation":24,"imageIndex":55},{"imageOffset":70084,"symbol":"_dispatch_main_queue_drain","symbolLocation":1184,"imageIndex":55},{"imageOffset":68884,"symbol":"_dispatch_main_queue_callback_4CF","symbolLocation":40,"imageIndex":55},{"imageOffset":601756,"symbol":"__CFRUNLOOP_IS_SERVICING_THE_MAIN_DISPATCH_QUEUE__","symbolLocation":12,"imageIndex":54},{"imageOffset":579752,"symbol":"__CFRunLoopRun","symbolLocation":1920,"imageIndex":54},{"imageOffset":576748,"symbol":"CFRunLoopRunSpecific","symbolLocation":536,"imageIndex":54},{"imageOffset":11520,"symbol":"GSEventRunModal","symbolLocation":164,"imageIndex":56},{"imageOffset":15067092,"symbol":"-[UIApplication _run]","symbolLocation":796,"imageIndex":57},{"imageOffset":15084448,"symbol":"UIApplicationMain","symbolLocation":124,"imageIndex":57},{"imageOffset":2264860,"imageIndex":57},{"imageOffset":49128,"sourceFile":"\/<compiler-generated>","symbol":"static UIApplicationDelegate.main()","symbolLocation":128,"imageIndex":2},{"imageOffset":48984,"sourceFile":"\/<compiler-generated>","symbol":"static AppDelegate.$main()","symbolLocation":44,"imageIndex":2},{"imageOffset":51388,"sourceFile":"\/<compiler-generated>","symbol":"__debug_main_executable_dylib_entry_point","symbolLocation":28,"imageIndex":2},{"imageOffset":4374549460,"imageIndex":58},{"imageOffset":35108,"symbol":"start","symbolLocation":6400,"imageIndex":0}]},{"id":5990658,"frames":[{"imageOffset":6532,"symbol":"start_wqthread","symbolLocation":0,"imageIndex":49}],"threadState":{"x":[{"value":6092992512},{"value":2819},{"value":6092455936},{"value":0},{"value":409604},{"value":18446744073709551615},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0}],"flavor":"ARM_THREAD_STATE64","lr":{"value":0},"cpsr":{"value":4096},"fp":{"value":0},"sp":{"value":6092992512},"esr":{"value":1442840704,"description":"(PC alignment)"},"pc":{"value":4390803844},"far":{"value":0}}},{"id":5990659,"frames":[{"imageOffset":6532,"symbol":"start_wqthread","symbolLocation":0,"imageIndex":49}],"threadState":{"x":[{"value":6093565952},{"value":6915},{"value":6093029376},{"value":0},{"value":409604},{"value":18446744073709551615},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0}],"flavor":"ARM_THREAD_STATE64","lr":{"value":0},"cpsr":{"value":4096},"fp":{"value":0},"sp":{"value":6093565952},"esr":{"value":1442840704,"description":"(PC alignment)"},"pc":{"value":4390803844},"far":{"value":0}}},{"id":5990669,"frames":[{"imageOffset":6532,"symbol":"start_wqthread","symbolLocation":0,"imageIndex":49}],"threadState":{"x":[{"value":6094139392},{"value":5379},{"value":6093602816},{"value":0},{"value":409604},{"value":18446744073709551615},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0}],"flavor":"ARM_THREAD_STATE64","lr":{"value":0},"cpsr":{"value":4096},"fp":{"value":0},"sp":{"value":6094139392},"esr":{"value":1442840704,"description":"(PC alignment)"},"pc":{"value":4390803844},"far":{"value":0}}},{"id":5990671,"frames":[{"imageOffset":6532,"symbol":"start_wqthread","symbolLocation":0,"imageIndex":49}],"threadState":{"x":[{"value":6094712832},{"value":9219},{"value":6094176256},{"value":0},{"value":409604},{"value":18446744073709551615},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0}],"flavor":"ARM_THREAD_STATE64","lr":{"value":0},"cpsr":{"value":4096},"fp":{"value":0},"sp":{"value":6094712832},"esr":{"value":1442840704,"description":"(PC alignment)"},"pc":{"value":4390803844},"far":{"value":0}}},{"id":5990672,"frames":[{"imageOffset":6532,"symbol":"start_wqthread","symbolLocation":0,"imageIndex":49}],"threadState":{"x":[{"value":6095286272},{"value":11011},{"value":6094749696},{"value":0},{"value":409604},{"value":18446744073709551615},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0}],"flavor":"ARM_THREAD_STATE64","lr":{"value":0},"cpsr":{"value":4096},"fp":{"value":0},"sp":{"value":6095286272},"esr":{"value":1442840704,"description":"(PC alignment)"},"pc":{"value":4390803844},"far":{"value":0}}},{"id":5990673,"name":"com.apple.uikit.eventfetch-thread","threadState":{"x":[{"value":268451845},{"value":21592279046},{"value":8589934592},{"value":58287001174016},{"value":4294967295},{"value":58287001174016},{"value":2},{"value":4294967295},{"value":0},{"value":17179869184},{"value":0},{"value":2},{"value":0},{"value":0},{"value":13571},{"value":3072},{"value":18446744073709551569},{"value":2564884475},{"value":0},{"value":4294967295},{"value":2},{"value":58287001174016},{"value":4294967295},{"value":58287001174016},{"value":6095854984},{"value":8589934592},{"value":21592279046},{"value":18446744073709550527},{"value":4412409862}],"flavor":"ARM_THREAD_STATE64","lr":{"value":4398995724},"cpsr":{"value":4096},"fp":{"value":6095854832},"sp":{"value":6095854752},"esr":{"value":1442840704,"description":"(PC alignment)"},"pc":{"value":4398926704},"far":{"value":0}},"frames":[{"imageOffset":2928,"symbol":"mach_msg2_trap","symbolLocation":8,"imageIndex":48},{"imageOffset":71948,"symbol":"mach_msg2_internal","symbolLocation":72,"imageIndex":48},{"imageOffset":35856,"symbol":"mach_msg_overwrite","symbolLocation":480,"imageIndex":48},{"imageOffset":3812,"symbol":"mach_msg","symbolLocation":20,"imageIndex":48},{"imageOffset":601028,"symbol":"__CFRunLoopServiceMachPort","symbolLocation":156,"imageIndex":54},{"imageOffset":578980,"symbol":"__CFRunLoopRun","symbolLocation":1148,"imageIndex":54},{"imageOffset":576748,"symbol":"CFRunLoopRunSpecific","symbolLocation":536,"imageIndex":54},{"imageOffset":7213964,"symbol":"-[NSRunLoop(NSRunLoop) runMode:beforeDate:]","symbolLocation":208,"imageIndex":59},{"imageOffset":7214508,"symbol":"-[NSRunLoop(NSRunLoop) runUntilDate:]","symbolLocation":60,"imageIndex":59},{"imageOffset":15778704,"symbol":"-[UIEventFetcher threadMain]","symbolLocation":408,"imageIndex":57},{"imageOffset":7373128,"symbol":"__NSThread__start__","symbolLocation":716,"imageIndex":59},{"imageOffset":26096,"symbol":"_pthread_start","symbolLocation":104,"imageIndex":49},{"imageOffset":6552,"symbol":"thread_start","symbolLocation":8,"imageIndex":49}]},{"id":5990674,"frames":[{"imageOffset":6532,"symbol":"start_wqthread","symbolLocation":0,"imageIndex":49}],"threadState":{"x":[{"value":6096433152},{"value":19971},{"value":6095896576},{"value":0},{"value":409604},{"value":18446744073709551615},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0}],"flavor":"ARM_THREAD_STATE64","lr":{"value":0},"cpsr":{"value":4096},"fp":{"value":0},"sp":{"value":6096433152},"esr":{"value":1442840704,"description":"(PC alignment)"},"pc":{"value":4390803844},"far":{"value":0}}},{"id":5990682,"frames":[{"imageOffset":6532,"symbol":"start_wqthread","symbolLocation":0,"imageIndex":49}],"threadState":{"x":[{"value":6097006592},{"value":19715},{"value":6096470016},{"value":0},{"value":409604},{"value":18446744073709551615},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0}],"flavor":"ARM_THREAD_STATE64","lr":{"value":0},"cpsr":{"value":4096},"fp":{"value":0},"sp":{"value":6097006592},"esr":{"value":1442840704,"description":"(PC alignment)"},"pc":{"value":4390803844},"far":{"value":0}}},{"id":5990683,"frames":[{"imageOffset":6532,"symbol":"start_wqthread","symbolLocation":0,"imageIndex":49}],"threadState":{"x":[{"value":6097580032},{"value":17923},{"value":6097043456},{"value":0},{"value":409604},{"value":18446744073709551615},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0}],"flavor":"ARM_THREAD_STATE64","lr":{"value":0},"cpsr":{"value":4096},"fp":{"value":0},"sp":{"value":6097580032},"esr":{"value":1442840704,"description":"(PC alignment)"},"pc":{"value":4390803844},"far":{"value":0}}},{"id":5990684,"frames":[{"imageOffset":6532,"symbol":"start_wqthread","symbolLocation":0,"imageIndex":49}],"threadState":{"x":[{"value":6098153472},{"value":19459},{"value":6097616896},{"value":0},{"value":409604},{"value":18446744073709551615},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0}],"flavor":"ARM_THREAD_STATE64","lr":{"value":0},"cpsr":{"value":4096},"fp":{"value":0},"sp":{"value":6098153472},"esr":{"value":1442840704,"description":"(PC alignment)"},"pc":{"value":4390803844},"far":{"value":0}}},{"id":5990685,"frames":[{"imageOffset":6532,"symbol":"start_wqthread","symbolLocation":0,"imageIndex":49}],"threadState":{"x":[{"value":6098726912},{"value":18179},{"value":6098190336},{"value":0},{"value":409604},{"value":18446744073709551615},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0}],"flavor":"ARM_THREAD_STATE64","lr":{"value":0},"cpsr":{"value":4096},"fp":{"value":0},"sp":{"value":6098726912},"esr":{"value":1442840704,"description":"(PC alignment)"},"pc":{"value":4390803844},"far":{"value":0}}},{"id":5990686,"frames":[{"imageOffset":6532,"symbol":"start_wqthread","symbolLocation":0,"imageIndex":49}],"threadState":{"x":[{"value":6099300352},{"value":19203},{"value":6098763776},{"value":0},{"value":409604},{"value":18446744073709551615},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0}],"flavor":"ARM_THREAD_STATE64","lr":{"value":0},"cpsr":{"value":4096},"fp":{"value":0},"sp":{"value":6099300352},"esr":{"value":1442840704,"description":"(PC alignment)"},"pc":{"value":4390803844},"far":{"value":0}}},{"id":5990687,"frames":[{"imageOffset":6532,"symbol":"start_wqthread","symbolLocation":0,"imageIndex":49}],"threadState":{"x":[{"value":6099873792},{"value":18435},{"value":6099337216},{"value":0},{"value":409604},{"value":18446744073709551615},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0}],"flavor":"ARM_THREAD_STATE64","lr":{"value":0},"cpsr":{"value":4096},"fp":{"value":0},"sp":{"value":6099873792},"esr":{"value":1442840704,"description":"(PC alignment)"},"pc":{"value":4390803844},"far":{"value":0}}},{"id":5990688,"frames":[{"imageOffset":6532,"symbol":"start_wqthread","symbolLocation":0,"imageIndex":49}],"threadState":{"x":[{"value":6100447232},{"value":18691},{"value":6099910656},{"value":0},{"value":409604},{"value":18446744073709551615},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0}],"flavor":"ARM_THREAD_STATE64","lr":{"value":0},"cpsr":{"value":4096},"fp":{"value":0},"sp":{"value":6100447232},"esr":{"value":1442840704,"description":"(PC alignment)"},"pc":{"value":4390803844},"far":{"value":0}}},{"id":5990689,"frames":[{"imageOffset":6532,"symbol":"start_wqthread","symbolLocation":0,"imageIndex":49}],"threadState":{"x":[{"value":6101020672},{"value":18947},{"value":6100484096},{"value":0},{"value":409604},{"value":18446744073709551615},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0}],"flavor":"ARM_THREAD_STATE64","lr":{"value":0},"cpsr":{"value":4096},"fp":{"value":0},"sp":{"value":6101020672},"esr":{"value":1442840704,"description":"(PC alignment)"},"pc":{"value":4390803844},"far":{"value":0}}},{"id":5990690,"frames":[{"imageOffset":6532,"symbol":"start_wqthread","symbolLocation":0,"imageIndex":49}],"threadState":{"x":[{"value":6101594112},{"value":21763},{"value":6101057536},{"value":0},{"value":409604},{"value":18446744073709551615},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0}],"flavor":"ARM_THREAD_STATE64","lr":{"value":0},"cpsr":{"value":4096},"fp":{"value":0},"sp":{"value":6101594112},"esr":{"value":1442840704,"description":"(PC alignment)"},"pc":{"value":4390803844},"far":{"value":0}}},{"id":5990691,"frames":[{"imageOffset":6532,"symbol":"start_wqthread","symbolLocation":0,"imageIndex":49}],"threadState":{"x":[{"value":6102167552},{"value":22019},{"value":6101630976},{"value":0},{"value":409604},{"value":18446744073709551615},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0}],"flavor":"ARM_THREAD_STATE64","lr":{"value":0},"cpsr":{"value":4096},"fp":{"value":0},"sp":{"value":6102167552},"esr":{"value":1442840704,"description":"(PC alignment)"},"pc":{"value":4390803844},"far":{"value":0}}},{"id":5990692,"frames":[{"imageOffset":6532,"symbol":"start_wqthread","symbolLocation":0,"imageIndex":49}],"threadState":{"x":[{"value":6102740992},{"value":32515},{"value":6102204416},{"value":0},{"value":409604},{"value":18446744073709551615},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0}],"flavor":"ARM_THREAD_STATE64","lr":{"value":0},"cpsr":{"value":4096},"fp":{"value":0},"sp":{"value":6102740992},"esr":{"value":1442840704,"description":"(PC alignment)"},"pc":{"value":4390803844},"far":{"value":0}}},{"id":5990693,"frames":[{"imageOffset":6532,"symbol":"start_wqthread","symbolLocation":0,"imageIndex":49}],"threadState":{"x":[{"value":6103314432},{"value":32259},{"value":6102777856},{"value":0},{"value":409604},{"value":18446744073709551615},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0}],"flavor":"ARM_THREAD_STATE64","lr":{"value":0},"cpsr":{"value":4096},"fp":{"value":0},"sp":{"value":6103314432},"esr":{"value":1442840704,"description":"(PC alignment)"},"pc":{"value":4390803844},"far":{"value":0}}},{"id":5990694,"frames":[{"imageOffset":6532,"symbol":"start_wqthread","symbolLocation":0,"imageIndex":49}],"threadState":{"x":[{"value":6103887872},{"value":22531},{"value":6103351296},{"value":0},{"value":409604},{"value":18446744073709551615},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0}],"flavor":"ARM_THREAD_STATE64","lr":{"value":0},"cpsr":{"value":4096},"fp":{"value":0},"sp":{"value":6103887872},"esr":{"value":1442840704,"description":"(PC alignment)"},"pc":{"value":4390803844},"far":{"value":0}}},{"id":5990700,"name":"io.flutter.1.raster","threadState":{"x":[{"value":268451845},{"value":21592279046},{"value":8589934592},{"value":111063559307264},{"value":0},{"value":111063559307264},{"value":2},{"value":4294967295},{"value":0},{"value":17179869184},{"value":0},{"value":2},{"value":0},{"value":0},{"value":25859},{"value":3072},{"value":18446744073709551569},{"value":2199023256066},{"value":0},{"value":4294967295},{"value":2},{"value":111063559307264},{"value":0},{"value":111063559307264},{"value":6106030088},{"value":8589934592},{"value":21592279046},{"value":18446744073709550527},{"value":4412409862}],"flavor":"ARM_THREAD_STATE64","lr":{"value":4398995724},"cpsr":{"value":4096},"fp":{"value":6106029936},"sp":{"value":6106029856},"esr":{"value":1442840704,"description":"(PC alignment)"},"pc":{"value":4398926704},"far":{"value":0}},"frames":[{"imageOffset":2928,"symbol":"mach_msg2_trap","symbolLocation":8,"imageIndex":48},{"imageOffset":71948,"symbol":"mach_msg2_internal","symbolLocation":72,"imageIndex":48},{"imageOffset":35856,"symbol":"mach_msg_overwrite","symbolLocation":480,"imageIndex":48},{"imageOffset":3812,"symbol":"mach_msg","symbolLocation":20,"imageIndex":48},{"imageOffset":601028,"symbol":"__CFRunLoopServiceMachPort","symbolLocation":156,"imageIndex":54},{"imageOffset":578980,"symbol":"__CFRunLoopRun","symbolLocation":1148,"imageIndex":54},{"imageOffset":576748,"symbol":"CFRunLoopRunSpecific","symbolLocation":536,"imageIndex":54},{"imageOffset":582712,"symbol":"fml::MessageLoopDarwin::Run()","symbolLocation":88,"imageIndex":46},{"imageOffset":555012,"symbol":"fml::MessageLoopImpl::DoRun()","symbolLocation":40,"imageIndex":46},{"imageOffset":577744,"symbol":"std::_fl::__function::__func<fml::Thread::Thread(std::_fl::function<void (fml::Thread::ThreadConfig const&)> const&, fml::Thread::ThreadConfig const&)::$_0, std::_fl::allocator<fml::Thread::Thread(std::_fl::function<void (fml::Thread::ThreadConfig const&)> const&, fml::Thread::ThreadConfig const&)::$_0>, void ()>::operator()()","symbolLocation":184,"imageIndex":46},{"imageOffset":576932,"symbol":"fml::ThreadHandle::ThreadHandle(std::_fl::function<void ()>&&)::$_0::__invoke(void*)","symbolLocation":36,"imageIndex":46},{"imageOffset":26096,"symbol":"_pthread_start","symbolLocation":104,"imageIndex":49},{"imageOffset":6552,"symbol":"thread_start","symbolLocation":8,"imageIndex":49}]},{"id":5990701,"name":"io.flutter.1.io","threadState":{"x":[{"value":268451845},{"value":21592279046},{"value":8589934592},{"value":118760140701696},{"value":0},{"value":118760140701696},{"value":2},{"value":4294967295},{"value":0},{"value":17179869184},{"value":0},{"value":2},{"value":0},{"value":0},{"value":27651},{"value":3072},{"value":18446744073709551569},{"value":2},{"value":0},{"value":4294967295},{"value":2},{"value":118760140701696},{"value":0},{"value":118760140701696},{"value":6108176392},{"value":8589934592},{"value":21592279046},{"value":18446744073709550527},{"value":4412409862}],"flavor":"ARM_THREAD_STATE64","lr":{"value":4398995724},"cpsr":{"value":4096},"fp":{"value":6108176240},"sp":{"value":6108176160},"esr":{"value":1442840704,"description":"(PC alignment)"},"pc":{"value":4398926704},"far":{"value":0}},"frames":[{"imageOffset":2928,"symbol":"mach_msg2_trap","symbolLocation":8,"imageIndex":48},{"imageOffset":71948,"symbol":"mach_msg2_internal","symbolLocation":72,"imageIndex":48},{"imageOffset":35856,"symbol":"mach_msg_overwrite","symbolLocation":480,"imageIndex":48},{"imageOffset":3812,"symbol":"mach_msg","symbolLocation":20,"imageIndex":48},{"imageOffset":601028,"symbol":"__CFRunLoopServiceMachPort","symbolLocation":156,"imageIndex":54},{"imageOffset":578980,"symbol":"__CFRunLoopRun","symbolLocation":1148,"imageIndex":54},{"imageOffset":576748,"symbol":"CFRunLoopRunSpecific","symbolLocation":536,"imageIndex":54},{"imageOffset":582712,"symbol":"fml::MessageLoopDarwin::Run()","symbolLocation":88,"imageIndex":46},{"imageOffset":555012,"symbol":"fml::MessageLoopImpl::DoRun()","symbolLocation":40,"imageIndex":46},{"imageOffset":577744,"symbol":"std::_fl::__function::__func<fml::Thread::Thread(std::_fl::function<void (fml::Thread::ThreadConfig const&)> const&, fml::Thread::ThreadConfig const&)::$_0, std::_fl::allocator<fml::Thread::Thread(std::_fl::function<void (fml::Thread::ThreadConfig const&)> const&, fml::Thread::ThreadConfig const&)::$_0>, void ()>::operator()()","symbolLocation":184,"imageIndex":46},{"imageOffset":576932,"symbol":"fml::ThreadHandle::ThreadHandle(std::_fl::function<void ()>&&)::$_0::__invoke(void*)","symbolLocation":36,"imageIndex":46},{"imageOffset":26096,"symbol":"_pthread_start","symbolLocation":104,"imageIndex":49},{"imageOffset":6552,"symbol":"thread_start","symbolLocation":8,"imageIndex":49}]},{"id":5990702,"name":"io.flutter.1.profiler","threadState":{"x":[{"value":268451845},{"value":21592279046},{"value":8589934592},{"value":144048908140544},{"value":0},{"value":144048908140544},{"value":2},{"value":4294967295},{"value":0},{"value":17179869184},{"value":0},{"value":2},{"value":0},{"value":0},{"value":33539},{"value":3072},{"value":18446744073709551569},{"value":2},{"value":0},{"value":4294967295},{"value":2},{"value":144048908140544},{"value":0},{"value":144048908140544},{"value":6110322696},{"value":8589934592},{"value":21592279046},{"value":18446744073709550527},{"value":4412409862}],"flavor":"ARM_THREAD_STATE64","lr":{"value":4398995724},"cpsr":{"value":4096},"fp":{"value":6110322544},"sp":{"value":6110322464},"esr":{"value":1442840704,"description":"(PC alignment)"},"pc":{"value":4398926704},"far":{"value":0}},"frames":[{"imageOffset":2928,"symbol":"mach_msg2_trap","symbolLocation":8,"imageIndex":48},{"imageOffset":71948,"symbol":"mach_msg2_internal","symbolLocation":72,"imageIndex":48},{"imageOffset":35856,"symbol":"mach_msg_overwrite","symbolLocation":480,"imageIndex":48},{"imageOffset":3812,"symbol":"mach_msg","symbolLocation":20,"imageIndex":48},{"imageOffset":601028,"symbol":"__CFRunLoopServiceMachPort","symbolLocation":156,"imageIndex":54},{"imageOffset":578980,"symbol":"__CFRunLoopRun","symbolLocation":1148,"imageIndex":54},{"imageOffset":576748,"symbol":"CFRunLoopRunSpecific","symbolLocation":536,"imageIndex":54},{"imageOffset":582712,"symbol":"fml::MessageLoopDarwin::Run()","symbolLocation":88,"imageIndex":46},{"imageOffset":555012,"symbol":"fml::MessageLoopImpl::DoRun()","symbolLocation":40,"imageIndex":46},{"imageOffset":577744,"symbol":"std::_fl::__function::__func<fml::Thread::Thread(std::_fl::function<void (fml::Thread::ThreadConfig const&)> const&, fml::Thread::ThreadConfig const&)::$_0, std::_fl::allocator<fml::Thread::Thread(std::_fl::function<void (fml::Thread::ThreadConfig const&)> const&, fml::Thread::ThreadConfig const&)::$_0>, void ()>::operator()()","symbolLocation":184,"imageIndex":46},{"imageOffset":576932,"symbol":"fml::ThreadHandle::ThreadHandle(std::_fl::function<void ()>&&)::$_0::__invoke(void*)","symbolLocation":36,"imageIndex":46},{"imageOffset":26096,"symbol":"_pthread_start","symbolLocation":104,"imageIndex":49},{"imageOffset":6552,"symbol":"thread_start","symbolLocation":8,"imageIndex":49}]},{"id":5990703,"name":"io.worker.1","threadState":{"x":[{"value":4},{"value":0},{"value":1024},{"value":0},{"value":0},{"value":160},{"value":0},{"value":0},{"value":6110899672},{"value":0},{"value":512},{"value":2199023256066},{"value":2199023256066},{"value":512},{"value":0},{"value":2199023256064},{"value":305},{"value":122},{"value":0},{"value":4404067144},{"value":4404067208},{"value":6110900448},{"value":0},{"value":0},{"value":1024},{"value":1024},{"value":1536},{"value":1},{"value":105553118498624}],"flavor":"ARM_THREAD_STATE64","lr":{"value":4390824632},"cpsr":{"value":1610616832},"fp":{"value":6110899792},"sp":{"value":6110899648},"esr":{"value":1442840704,"description":"(PC alignment)"},"pc":{"value":4398940192},"far":{"value":0}},"frames":[{"imageOffset":16416,"symbol":"__psynch_cvwait","symbolLocation":8,"imageIndex":48},{"imageOffset":27320,"symbol":"_pthread_cond_wait","symbolLocation":976,"imageIndex":49},{"imageOffset":411992,"symbol":"std::_fl::condition_variable::wait(std::_fl::unique_lock<std::_fl::mutex>&)","symbolLocation":24,"imageIndex":46},{"imageOffset":540212,"symbol":"fml::ConcurrentMessageLoop::WorkerMain()","symbolLocation":140,"imageIndex":46},{"imageOffset":542560,"symbol":"void* std::_fl::__thread_proxy[abi:nn210000]<std::_fl::tuple<std::_fl::unique_ptr<std::_fl::__thread_struct, std::_fl::default_delete<std::_fl::__thread_struct>>, fml::ConcurrentMessageLoop::ConcurrentMessageLoop(unsigned long)::$_0>>(void*)","symbolLocation":212,"imageIndex":46},{"imageOffset":26096,"symbol":"_pthread_start","symbolLocation":104,"imageIndex":49},{"imageOffset":6552,"symbol":"thread_start","symbolLocation":8,"imageIndex":49}]},{"id":5990704,"name":"io.worker.2","threadState":{"x":[{"value":4},{"value":0},{"value":1024},{"value":0},{"value":0},{"value":160},{"value":0},{"value":0},{"value":6111473112},{"value":0},{"value":512},{"value":2199023256066},{"value":2199023256066},{"value":512},{"value":0},{"value":2199023256064},{"value":305},{"value":92},{"value":0},{"value":4404067144},{"value":4404067208},{"value":6111473888},{"value":0},{"value":0},{"value":1024},{"value":1024},{"value":1792},{"value":1},{"value":105553118497664}],"flavor":"ARM_THREAD_STATE64","lr":{"value":4390824632},"cpsr":{"value":1610616832},"fp":{"value":6111473232},"sp":{"value":6111473088},"esr":{"value":1442840704,"description":"(PC alignment)"},"pc":{"value":4398940192},"far":{"value":0}},"frames":[{"imageOffset":16416,"symbol":"__psynch_cvwait","symbolLocation":8,"imageIndex":48},{"imageOffset":27320,"symbol":"_pthread_cond_wait","symbolLocation":976,"imageIndex":49},{"imageOffset":411992,"symbol":"std::_fl::condition_variable::wait(std::_fl::unique_lock<std::_fl::mutex>&)","symbolLocation":24,"imageIndex":46},{"imageOffset":540212,"symbol":"fml::ConcurrentMessageLoop::WorkerMain()","symbolLocation":140,"imageIndex":46},{"imageOffset":542560,"symbol":"void* std::_fl::__thread_proxy[abi:nn210000]<std::_fl::tuple<std::_fl::unique_ptr<std::_fl::__thread_struct, std::_fl::default_delete<std::_fl::__thread_struct>>, fml::ConcurrentMessageLoop::ConcurrentMessageLoop(unsigned long)::$_0>>(void*)","symbolLocation":212,"imageIndex":46},{"imageOffset":26096,"symbol":"_pthread_start","symbolLocation":104,"imageIndex":49},{"imageOffset":6552,"symbol":"thread_start","symbolLocation":8,"imageIndex":49}]},{"id":5990705,"name":"io.worker.3","threadState":{"x":[{"value":4},{"value":0},{"value":1024},{"value":0},{"value":0},{"value":160},{"value":0},{"value":0},{"value":6112046552},{"value":0},{"value":256},{"value":1099511628034},{"value":1099511628034},{"value":256},{"value":0},{"value":1099511628032},{"value":305},{"value":65},{"value":0},{"value":4404067144},{"value":4404067208},{"value":6112047328},{"value":0},{"value":0},{"value":1024},{"value":1025},{"value":1280},{"value":1},{"value":105553118496800}],"flavor":"ARM_THREAD_STATE64","lr":{"value":4390824632},"cpsr":{"value":1610616832},"fp":{"value":6112046672},"sp":{"value":6112046528},"esr":{"value":1442840704,"description":"(PC alignment)"},"pc":{"value":4398940192},"far":{"value":0}},"frames":[{"imageOffset":16416,"symbol":"__psynch_cvwait","symbolLocation":8,"imageIndex":48},{"imageOffset":27320,"symbol":"_pthread_cond_wait","symbolLocation":976,"imageIndex":49},{"imageOffset":411992,"symbol":"std::_fl::condition_variable::wait(std::_fl::unique_lock<std::_fl::mutex>&)","symbolLocation":24,"imageIndex":46},{"imageOffset":540212,"symbol":"fml::ConcurrentMessageLoop::WorkerMain()","symbolLocation":140,"imageIndex":46},{"imageOffset":542560,"symbol":"void* std::_fl::__thread_proxy[abi:nn210000]<std::_fl::tuple<std::_fl::unique_ptr<std::_fl::__thread_struct, std::_fl::default_delete<std::_fl::__thread_struct>>, fml::ConcurrentMessageLoop::ConcurrentMessageLoop(unsigned long)::$_0>>(void*)","symbolLocation":212,"imageIndex":46},{"imageOffset":26096,"symbol":"_pthread_start","symbolLocation":104,"imageIndex":49},{"imageOffset":6552,"symbol":"thread_start","symbolLocation":8,"imageIndex":49}]},{"id":5990706,"name":"io.worker.4","threadState":{"x":[{"value":260},{"value":0},{"value":1024},{"value":0},{"value":0},{"value":160},{"value":0},{"value":0},{"value":6112619992},{"value":0},{"value":512},{"value":2199023256066},{"value":2199023256066},{"value":512},{"value":0},{"value":2199023256064},{"value":305},{"value":142},{"value":0},{"value":4404067144},{"value":4404067208},{"value":6112620768},{"value":0},{"value":0},{"value":1024},{"value":1024},{"value":2048},{"value":1},{"value":105553118499264}],"flavor":"ARM_THREAD_STATE64","lr":{"value":4390824632},"cpsr":{"value":1610616832},"fp":{"value":6112620112},"sp":{"value":6112619968},"esr":{"value":1442840704,"description":"(PC alignment)"},"pc":{"value":4398940192},"far":{"value":0}},"frames":[{"imageOffset":16416,"symbol":"__psynch_cvwait","symbolLocation":8,"imageIndex":48},{"imageOffset":27320,"symbol":"_pthread_cond_wait","symbolLocation":976,"imageIndex":49},{"imageOffset":411992,"symbol":"std::_fl::condition_variable::wait(std::_fl::unique_lock<std::_fl::mutex>&)","symbolLocation":24,"imageIndex":46},{"imageOffset":540212,"symbol":"fml::ConcurrentMessageLoop::WorkerMain()","symbolLocation":140,"imageIndex":46},{"imageOffset":542560,"symbol":"void* std::_fl::__thread_proxy[abi:nn210000]<std::_fl::tuple<std::_fl::unique_ptr<std::_fl::__thread_struct, std::_fl::default_delete<std::_fl::__thread_struct>>, fml::ConcurrentMessageLoop::ConcurrentMessageLoop(unsigned long)::$_0>>(void*)","symbolLocation":212,"imageIndex":46},{"imageOffset":26096,"symbol":"_pthread_start","symbolLocation":104,"imageIndex":49},{"imageOffset":6552,"symbol":"thread_start","symbolLocation":8,"imageIndex":49}]},{"id":5990707,"name":"dart:io EventHandler","threadState":{"x":[{"value":4},{"value":0},{"value":0},{"value":6113717528},{"value":16},{"value":6113716488},{"value":105553118556032},{"value":0},{"value":998000000},{"value":119},{"value":244838346588160},{"value":32},{"value":105553116269320},{"value":2095104},{"value":2043},{"value":4167328125},{"value":363},{"value":4169422990},{"value":0},{"value":105553156117184},{"value":6113716488},{"value":67108864},{"value":2147483647},{"value":274877907},{"value":4294966296},{"value":1000000},{"value":210151666},{"value":0},{"value":0}],"flavor":"ARM_THREAD_STATE64","lr":{"value":4472086008},"cpsr":{"value":536875008},"fp":{"value":6113718128},"sp":{"value":6113716464},"esr":{"value":1442840704,"description":"(PC alignment)"},"pc":{"value":4398949980},"far":{"value":0}},"frames":[{"imageOffset":26204,"symbol":"kevent","symbolLocation":8,"imageIndex":48},{"imageOffset":5053944,"symbol":"dart::bin::EventHandlerImplementation::EventHandlerEntry(unsigned long)","symbolLocation":300,"imageIndex":46},{"imageOffset":5168976,"symbol":"dart::bin::ThreadStart(void*)","symbolLocation":88,"imageIndex":46},{"imageOffset":26096,"symbol":"_pthread_start","symbolLocation":104,"imageIndex":49},{"imageOffset":6552,"symbol":"thread_start","symbolLocation":8,"imageIndex":49}]},{"id":5990708,"name":"DartWorker","threadState":{"x":[{"value":260},{"value":0},{"value":6400},{"value":0},{"value":0},{"value":160},{"value":4},{"value":991971000},{"value":6401},{"value":0},{"value":1280},{"value":5497558140162},{"value":5497558140162},{"value":1280},{"value":0},{"value":5497558140160},{"value":305},{"value":28},{"value":0},{"value":4404068136},{"value":105553156121648},{"value":1},{"value":991971000},{"value":4},{"value":6400},{"value":6401},{"value":6656},{"value":4502900736,"symbolLocation":3816,"symbol":"dart::Symbols::symbol_handles_"},{"value":1000}],"flavor":"ARM_THREAD_STATE64","lr":{"value":4390824676},"cpsr":{"value":2684358656},"fp":{"value":6114814528},"sp":{"value":6114814384},"esr":{"value":1442840704,"description":"(PC alignment)"},"pc":{"value":4398940192},"far":{"value":0}},"frames":[{"imageOffset":16416,"symbol":"__psynch_cvwait","symbolLocation":8,"imageIndex":48},{"imageOffset":27364,"symbol":"_pthread_cond_wait","symbolLocation":1020,"imageIndex":49},{"imageOffset":5477960,"symbol":"dart::ConditionVariable::WaitMicros(dart::Mutex*, long long)","symbolLocation":112,"imageIndex":46},{"imageOffset":6910828,"symbol":"dart::ThreadPool::WorkerLoop(dart::ThreadPool::Worker*)","symbolLocation":508,"imageIndex":46},{"imageOffset":6911172,"symbol":"dart::ThreadPool::Worker::Main(unsigned long)","symbolLocation":116,"imageIndex":46},{"imageOffset":6610980,"symbol":"dart::ThreadStart(void*)","symbolLocation":204,"imageIndex":46},{"imageOffset":26096,"symbol":"_pthread_start","symbolLocation":104,"imageIndex":49},{"imageOffset":6552,"symbol":"thread_start","symbolLocation":8,"imageIndex":49}]},{"id":5990711,"name":"DartWorker","threadState":{"x":[{"value":260},{"value":0},{"value":0},{"value":0},{"value":0},{"value":160},{"value":5},{"value":0},{"value":1},{"value":0},{"value":0},{"value":2},{"value":2},{"value":0},{"value":0},{"value":0},{"value":305},{"value":247},{"value":0},{"value":4463807160},{"value":105553156201168},{"value":1},{"value":0},{"value":5},{"value":0},{"value":1},{"value":256},{"value":4502900736,"symbolLocation":3816,"symbol":"dart::Symbols::symbol_handles_"},{"value":1000}],"flavor":"ARM_THREAD_STATE64","lr":{"value":4390824676},"cpsr":{"value":2684358656},"fp":{"value":6115912256},"sp":{"value":6115912112},"esr":{"value":1442840704,"description":"(PC alignment)"},"pc":{"value":4398940192},"far":{"value":0}},"frames":[{"imageOffset":16416,"symbol":"__psynch_cvwait","symbolLocation":8,"imageIndex":48},{"imageOffset":27364,"symbol":"_pthread_cond_wait","symbolLocation":1020,"imageIndex":49},{"imageOffset":5477960,"symbol":"dart::ConditionVariable::WaitMicros(dart::Mutex*, long long)","symbolLocation":112,"imageIndex":46},{"imageOffset":6910828,"symbol":"dart::ThreadPool::WorkerLoop(dart::ThreadPool::Worker*)","symbolLocation":508,"imageIndex":46},{"imageOffset":6911172,"symbol":"dart::ThreadPool::Worker::Main(unsigned long)","symbolLocation":116,"imageIndex":46},{"imageOffset":6610980,"symbol":"dart::ThreadStart(void*)","symbolLocation":204,"imageIndex":46},{"imageOffset":26096,"symbol":"_pthread_start","symbolLocation":104,"imageIndex":49},{"imageOffset":6552,"symbol":"thread_start","symbolLocation":8,"imageIndex":49}]},{"id":5990712,"name":"DartWorker","threadState":{"x":[{"value":260},{"value":0},{"value":256},{"value":0},{"value":0},{"value":160},{"value":61},{"value":0},{"value":257},{"value":0},{"value":0},{"value":2},{"value":2},{"value":0},{"value":0},{"value":0},{"value":305},{"value":310},{"value":0},{"value":4406176280},{"value":105553156200784},{"value":1},{"value":0},{"value":61},{"value":256},{"value":257},{"value":512},{"value":4502900736,"symbolLocation":3816,"symbol":"dart::Symbols::symbol_handles_"},{"value":1000}],"flavor":"ARM_THREAD_STATE64","lr":{"value":4390824676},"cpsr":{"value":2684358656},"fp":{"value":6117009904},"sp":{"value":6117009760},"esr":{"value":1442840704,"description":"(PC alignment)"},"pc":{"value":4398940192},"far":{"value":0}},"frames":[{"imageOffset":16416,"symbol":"__psynch_cvwait","symbolLocation":8,"imageIndex":48},{"imageOffset":27364,"symbol":"_pthread_cond_wait","symbolLocation":1020,"imageIndex":49},{"imageOffset":5477960,"symbol":"dart::ConditionVariable::WaitMicros(dart::Mutex*, long long)","symbolLocation":112,"imageIndex":46},{"imageOffset":5863564,"symbol":"dart::MutatorThreadPool::OnEnterIdleLocked(dart::MutexLocker*, dart::ThreadPool::Worker*)","symbolLocation":152,"imageIndex":46},{"imageOffset":6910444,"symbol":"dart::ThreadPool::WorkerLoop(dart::ThreadPool::Worker*)","symbolLocation":124,"imageIndex":46},{"imageOffset":6911172,"symbol":"dart::ThreadPool::Worker::Main(unsigned long)","symbolLocation":116,"imageIndex":46},{"imageOffset":6610980,"symbol":"dart::ThreadStart(void*)","symbolLocation":204,"imageIndex":46},{"imageOffset":26096,"symbol":"_pthread_start","symbolLocation":104,"imageIndex":49},{"imageOffset":6552,"symbol":"thread_start","symbolLocation":8,"imageIndex":49}]},{"id":5990739,"name":"caulk.messenger.shared:17","threadState":{"x":[{"value":14},{"value":105553118696410},{"value":0},{"value":6117585002},{"value":105553118696384},{"value":25},{"value":0},{"value":0},{"value":0},{"value":4294967295},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":18446744073709551580},{"value":0},{"value":0},{"value":105553176441056},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0}],"flavor":"ARM_THREAD_STATE64","lr":{"value":7360289596},"cpsr":{"value":2147487744},"fp":{"value":6117584768},"sp":{"value":6117584736},"esr":{"value":1442840704,"description":"(PC alignment)"},"pc":{"value":4398926572},"far":{"value":0}},"frames":[{"imageOffset":2796,"symbol":"semaphore_wait_trap","symbolLocation":8,"imageIndex":48},{"imageOffset":64316,"symbol":"caulk::semaphore::timed_wait(double)","symbolLocation":220,"imageIndex":60},{"imageOffset":93248,"symbol":"caulk::concurrent::details::worker_thread::run()","symbolLocation":28,"imageIndex":60},{"imageOffset":93364,"symbol":"void* caulk::thread_proxy<std::__1::tuple<caulk::thread::attributes, void (caulk::concurrent::details::worker_thread::*)(), std::__1::tuple<caulk::concurrent::details::worker_thread*>>>(void*)","symbolLocation":48,"imageIndex":60},{"imageOffset":26096,"symbol":"_pthread_start","symbolLocation":104,"imageIndex":49},{"imageOffset":6552,"symbol":"thread_start","symbolLocation":8,"imageIndex":49}]},{"id":5990740,"name":"caulk.messenger.shared:high","threadState":{"x":[{"value":14},{"value":105553118696540},{"value":0},{"value":6118158444},{"value":105553118696512},{"value":27},{"value":0},{"value":0},{"value":0},{"value":4294967295},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":18446744073709551580},{"value":0},{"value":0},{"value":105553176441680},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0}],"flavor":"ARM_THREAD_STATE64","lr":{"value":7360289596},"cpsr":{"value":2147487744},"fp":{"value":6118158208},"sp":{"value":6118158176},"esr":{"value":1442840704,"description":"(PC alignment)"},"pc":{"value":4398926572},"far":{"value":0}},"frames":[{"imageOffset":2796,"symbol":"semaphore_wait_trap","symbolLocation":8,"imageIndex":48},{"imageOffset":64316,"symbol":"caulk::semaphore::timed_wait(double)","symbolLocation":220,"imageIndex":60},{"imageOffset":93248,"symbol":"caulk::concurrent::details::worker_thread::run()","symbolLocation":28,"imageIndex":60},{"imageOffset":93364,"symbol":"void* caulk::thread_proxy<std::__1::tuple<caulk::thread::attributes, void (caulk::concurrent::details::worker_thread::*)(), std::__1::tuple<caulk::concurrent::details::worker_thread*>>>(void*)","symbolLocation":48,"imageIndex":60},{"imageOffset":26096,"symbol":"_pthread_start","symbolLocation":104,"imageIndex":49},{"imageOffset":6552,"symbol":"thread_start","symbolLocation":8,"imageIndex":49}]},{"id":5990758,"name":"DartWorker","threadState":{"x":[{"value":260},{"value":0},{"value":3840},{"value":0},{"value":0},{"value":160},{"value":5},{"value":0},{"value":3841},{"value":0},{"value":1280},{"value":5497558140162},{"value":5497558140162},{"value":1280},{"value":0},{"value":5497558140160},{"value":305},{"value":273},{"value":0},{"value":4404068136},{"value":105553156268048},{"value":1},{"value":0},{"value":5},{"value":3840},{"value":3841},{"value":4096},{"value":4502900736,"symbolLocation":3816,"symbol":"dart::Symbols::symbol_handles_"},{"value":1000}],"flavor":"ARM_THREAD_STATE64","lr":{"value":4390824676},"cpsr":{"value":2684358656},"fp":{"value":6119254592},"sp":{"value":6119254448},"esr":{"value":1442840704,"description":"(PC alignment)"},"pc":{"value":4398940192},"far":{"value":0}},"frames":[{"imageOffset":16416,"symbol":"__psynch_cvwait","symbolLocation":8,"imageIndex":48},{"imageOffset":27364,"symbol":"_pthread_cond_wait","symbolLocation":1020,"imageIndex":49},{"imageOffset":5477960,"symbol":"dart::ConditionVariable::WaitMicros(dart::Mutex*, long long)","symbolLocation":112,"imageIndex":46},{"imageOffset":6910828,"symbol":"dart::ThreadPool::WorkerLoop(dart::ThreadPool::Worker*)","symbolLocation":508,"imageIndex":46},{"imageOffset":6911172,"symbol":"dart::ThreadPool::Worker::Main(unsigned long)","symbolLocation":116,"imageIndex":46},{"imageOffset":6610980,"symbol":"dart::ThreadStart(void*)","symbolLocation":204,"imageIndex":46},{"imageOffset":26096,"symbol":"_pthread_start","symbolLocation":104,"imageIndex":49},{"imageOffset":6552,"symbol":"thread_start","symbolLocation":8,"imageIndex":49}]}],
  "usedImages" : [
  {
    "source" : "P",
    "arch" : "arm64e",
    "base" : 4375543808,
    "size" : 655360,
    "uuid" : "ff357121-622e-3ea0-9ef0-8d706f44dc1a",
    "path" : "\/usr\/lib\/dyld",
    "name" : "dyld"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4374446080,
    "CFBundleShortVersionString" : "1.0.0",
    "CFBundleIdentifier" : "com.hopenapp.hopen",
    "size" : 16384,
    "uuid" : "caf7731a-2c8a-3262-ac04-f72f06f73eab",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/24942F86-3060-4B2A-962B-60BD9614F1F4\/data\/Containers\/Bundle\/Application\/13FF1FEF-7257-49BB-8287-1FCD0810CD0E\/Runner.app\/Runner",
    "name" : "Runner",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4377837568,
    "size" : 540672,
    "uuid" : "c14c9eb6-9864-3bfd-b8df-604b8f776e52",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/24942F86-3060-4B2A-962B-60BD9614F1F4\/data\/Containers\/Bundle\/Application\/13FF1FEF-7257-49BB-8287-1FCD0810CD0E\/Runner.app\/Runner.debug.dylib",
    "name" : "Runner.debug.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4377182208,
    "CFBundleShortVersionString" : "1.7.6",
    "CFBundleIdentifier" : "org.cocoapods.AppAuth",
    "size" : 163840,
    "uuid" : "cd329467-4e64-33fd-8f81-c140b9c607a9",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/24942F86-3060-4B2A-962B-60BD9614F1F4\/data\/Containers\/Bundle\/Application\/13FF1FEF-7257-49BB-8287-1FCD0810CD0E\/Runner.app\/Frameworks\/AppAuth.framework\/AppAuth",
    "name" : "AppAuth",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4378869760,
    "CFBundleShortVersionString" : "11.2.0",
    "CFBundleIdentifier" : "org.cocoapods.AppCheckCore",
    "size" : 147456,
    "uuid" : "31ce7028-5a68-39f1-a840-891714831d69",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/24942F86-3060-4B2A-962B-60BD9614F1F4\/data\/Containers\/Bundle\/Application\/13FF1FEF-7257-49BB-8287-1FCD0810CD0E\/Runner.app\/Frameworks\/AppCheckCore.framework\/AppCheckCore",
    "name" : "AppCheckCore",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4380803072,
    "CFBundleShortVersionString" : "4.3.9",
    "CFBundleIdentifier" : "org.cocoapods.DKImagePickerController",
    "size" : 638976,
    "uuid" : "03fcf312-d00e-3900-9646-92f66cbdaf51",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/24942F86-3060-4B2A-962B-60BD9614F1F4\/data\/Containers\/Bundle\/Application\/13FF1FEF-7257-49BB-8287-1FCD0810CD0E\/Runner.app\/Frameworks\/DKImagePickerController.framework\/DKImagePickerController",
    "name" : "DKImagePickerController",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4382375936,
    "CFBundleShortVersionString" : "0.0.19",
    "CFBundleIdentifier" : "org.cocoapods.DKPhotoGallery",
    "size" : 557056,
    "uuid" : "9cc3f835-0645-3b4c-962e-78404b6674c6",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/24942F86-3060-4B2A-962B-60BD9614F1F4\/data\/Containers\/Bundle\/Application\/13FF1FEF-7257-49BB-8287-1FCD0810CD0E\/Runner.app\/Frameworks\/DKPhotoGallery.framework\/DKPhotoGallery",
    "name" : "DKPhotoGallery",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4376805376,
    "CFBundleShortVersionString" : "2.4.0",
    "CFBundleIdentifier" : "org.cocoapods.FBLPromises",
    "size" : 81920,
    "uuid" : "962c6d34-cde2-3ecf-ba9b-3af78f34ab5a",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/24942F86-3060-4B2A-962B-60BD9614F1F4\/data\/Containers\/Bundle\/Application\/13FF1FEF-7257-49BB-8287-1FCD0810CD0E\/Runner.app\/Frameworks\/FBLPromises.framework\/FBLPromises",
    "name" : "FBLPromises",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4377542656,
    "CFBundleShortVersionString" : "11.15.0",
    "CFBundleIdentifier" : "org.cocoapods.FirebaseCore",
    "size" : 81920,
    "uuid" : "5e0d33ee-5834-3d02-972b-27a987c049e3",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/24942F86-3060-4B2A-962B-60BD9614F1F4\/data\/Containers\/Bundle\/Application\/13FF1FEF-7257-49BB-8287-1FCD0810CD0E\/Runner.app\/Frameworks\/FirebaseCore.framework\/FirebaseCore",
    "name" : "FirebaseCore",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4379738112,
    "CFBundleShortVersionString" : "11.15.0",
    "CFBundleIdentifier" : "org.cocoapods.FirebaseCoreInternal",
    "size" : 163840,
    "uuid" : "29efc00f-307c-3f23-80fa-534b89bd3c78",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/24942F86-3060-4B2A-962B-60BD9614F1F4\/data\/Containers\/Bundle\/Application\/13FF1FEF-7257-49BB-8287-1FCD0810CD0E\/Runner.app\/Frameworks\/FirebaseCoreInternal.framework\/FirebaseCoreInternal",
    "name" : "FirebaseCoreInternal",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4380229632,
    "CFBundleShortVersionString" : "11.15.0",
    "CFBundleIdentifier" : "org.cocoapods.FirebaseInstallations",
    "size" : 114688,
    "uuid" : "d9fbd69a-5b18-38a7-adc4-499fc338c58c",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/24942F86-3060-4B2A-962B-60BD9614F1F4\/data\/Containers\/Bundle\/Application\/13FF1FEF-7257-49BB-8287-1FCD0810CD0E\/Runner.app\/Frameworks\/FirebaseInstallations.framework\/FirebaseInstallations",
    "name" : "FirebaseInstallations",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4384276480,
    "CFBundleShortVersionString" : "11.15.0",
    "CFBundleIdentifier" : "org.cocoapods.FirebaseMessaging",
    "size" : 294912,
    "uuid" : "2decb982-fd1b-3698-ade5-50d8b05739d2",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/24942F86-3060-4B2A-962B-60BD9614F1F4\/data\/Containers\/Bundle\/Application\/13FF1FEF-7257-49BB-8287-1FCD0810CD0E\/Runner.app\/Frameworks\/FirebaseMessaging.framework\/FirebaseMessaging",
    "name" : "FirebaseMessaging",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4383670272,
    "CFBundleShortVersionString" : "4.1.1",
    "CFBundleIdentifier" : "org.cocoapods.GTMAppAuth",
    "size" : 147456,
    "uuid" : "b56e79ea-28fb-3a59-b431-e8fc09b71b48",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/24942F86-3060-4B2A-962B-60BD9614F1F4\/data\/Containers\/Bundle\/Application\/13FF1FEF-7257-49BB-8287-1FCD0810CD0E\/Runner.app\/Frameworks\/GTMAppAuth.framework\/GTMAppAuth",
    "name" : "GTMAppAuth",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4385521664,
    "CFBundleShortVersionString" : "3.5.0",
    "CFBundleIdentifier" : "org.cocoapods.GTMSessionFetcher",
    "size" : 360448,
    "uuid" : "205fdd37-40d9-38fe-b575-614ac31c7140",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/24942F86-3060-4B2A-962B-60BD9614F1F4\/data\/Containers\/Bundle\/Application\/13FF1FEF-7257-49BB-8287-1FCD0810CD0E\/Runner.app\/Frameworks\/GTMSessionFetcher.framework\/GTMSessionFetcher",
    "name" : "GTMSessionFetcher",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4384866304,
    "CFBundleShortVersionString" : "10.1.0",
    "CFBundleIdentifier" : "org.cocoapods.GoogleDataTransport",
    "size" : 212992,
    "uuid" : "a5c335de-684e-3b83-ba64-e21e447ea889",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/24942F86-3060-4B2A-962B-60BD9614F1F4\/data\/Containers\/Bundle\/Application\/13FF1FEF-7257-49BB-8287-1FCD0810CD0E\/Runner.app\/Frameworks\/GoogleDataTransport.framework\/GoogleDataTransport",
    "name" : "GoogleDataTransport",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4386160640,
    "CFBundleShortVersionString" : "8.0.0",
    "CFBundleIdentifier" : "org.cocoapods.GoogleSignIn",
    "size" : 147456,
    "uuid" : "55fb2deb-d907-305e-a508-ee748f35ff18",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/24942F86-3060-4B2A-962B-60BD9614F1F4\/data\/Containers\/Bundle\/Application\/13FF1FEF-7257-49BB-8287-1FCD0810CD0E\/Runner.app\/Frameworks\/GoogleSignIn.framework\/GoogleSignIn",
    "name" : "GoogleSignIn",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4386504704,
    "CFBundleShortVersionString" : "8.1.0",
    "CFBundleIdentifier" : "org.cocoapods.GoogleUtilities",
    "size" : 147456,
    "uuid" : "579d30f5-873d-3cac-afaa-2ef68808afd3",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/24942F86-3060-4B2A-962B-60BD9614F1F4\/data\/Containers\/Bundle\/Application\/13FF1FEF-7257-49BB-8287-1FCD0810CD0E\/Runner.app\/Frameworks\/GoogleUtilities.framework\/GoogleUtilities",
    "name" : "GoogleUtilities",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4375314432,
    "CFBundleShortVersionString" : "2.2.0",
    "CFBundleIdentifier" : "org.cocoapods.Mantle",
    "size" : 114688,
    "uuid" : "6b831635-7679-349a-8447-49703eebba25",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/24942F86-3060-4B2A-962B-60BD9614F1F4\/data\/Containers\/Bundle\/Application\/13FF1FEF-7257-49BB-8287-1FCD0810CD0E\/Runner.app\/Frameworks\/Mantle.framework\/Mantle",
    "name" : "Mantle",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4391993344,
    "CFBundleShortVersionString" : "3.29.0",
    "CFBundleIdentifier" : "org.cocoapods.PostHog",
    "size" : 2310144,
    "uuid" : "780581b9-7198-311c-820d-77468c8d52c9",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/24942F86-3060-4B2A-962B-60BD9614F1F4\/data\/Containers\/Bundle\/Application\/13FF1FEF-7257-49BB-8287-1FCD0810CD0E\/Runner.app\/Frameworks\/PostHog.framework\/PostHog",
    "name" : "PostHog",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4379443200,
    "CFBundleShortVersionString" : "5.2.4",
    "CFBundleIdentifier" : "org.cocoapods.Reachability",
    "size" : 49152,
    "uuid" : "d5570c0a-e256-3e20-8732-c2b425cc2bce",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/24942F86-3060-4B2A-962B-60BD9614F1F4\/data\/Containers\/Bundle\/Application\/13FF1FEF-7257-49BB-8287-1FCD0810CD0E\/Runner.app\/Frameworks\/Reachability.framework\/Reachability",
    "name" : "Reachability",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4387864576,
    "CFBundleShortVersionString" : "5.21.1",
    "CFBundleIdentifier" : "org.cocoapods.SDWebImage",
    "size" : 524288,
    "uuid" : "43368b18-84f3-3020-bd85-9da338c90e8a",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/24942F86-3060-4B2A-962B-60BD9614F1F4\/data\/Containers\/Bundle\/Application\/13FF1FEF-7257-49BB-8287-1FCD0810CD0E\/Runner.app\/Frameworks\/SDWebImage.framework\/SDWebImage",
    "name" : "SDWebImage",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4379230208,
    "CFBundleShortVersionString" : "0.14.6",
    "CFBundleIdentifier" : "org.cocoapods.SDWebImageWebPCoder",
    "size" : 49152,
    "uuid" : "070de3a9-68cf-36c8-965a-e06b41c095bc",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/24942F86-3060-4B2A-962B-60BD9614F1F4\/data\/Containers\/Bundle\/Application\/13FF1FEF-7257-49BB-8287-1FCD0810CD0E\/Runner.app\/Frameworks\/SDWebImageWebPCoder.framework\/SDWebImageWebPCoder",
    "name" : "SDWebImageWebPCoder",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4386816000,
    "CFBundleShortVersionString" : "5.4.5",
    "CFBundleIdentifier" : "org.cocoapods.SwiftyGif",
    "size" : 98304,
    "uuid" : "88cedd7c-e176-32e9-aaa4-99c7c8e78da9",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/24942F86-3060-4B2A-962B-60BD9614F1F4\/data\/Containers\/Bundle\/Application\/13FF1FEF-7257-49BB-8287-1FCD0810CD0E\/Runner.app\/Frameworks\/SwiftyGif.framework\/SwiftyGif",
    "name" : "SwiftyGif",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4407754752,
    "CFBundleShortVersionString" : "1.0",
    "CFBundleIdentifier" : "org.webrtc.WebRTC",
    "size" : 9666560,
    "uuid" : "4c4c4404-5555-3144-a1e8-358e2ad27d7b",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/24942F86-3060-4B2A-962B-60BD9614F1F4\/data\/Containers\/Bundle\/Application\/13FF1FEF-7257-49BB-8287-1FCD0810CD0E\/Runner.app\/Frameworks\/WebRTC.framework\/WebRTC",
    "name" : "WebRTC",
    "CFBundleVersion" : "1.0"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4387471360,
    "CFBundleShortVersionString" : "0.0.1",
    "CFBundleIdentifier" : "org.cocoapods.audioplayers-darwin",
    "size" : 131072,
    "uuid" : "5dba94ce-5e49-3df4-a5dc-1b20006850a3",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/24942F86-3060-4B2A-962B-60BD9614F1F4\/data\/Containers\/Bundle\/Application\/13FF1FEF-7257-49BB-8287-1FCD0810CD0E\/Runner.app\/Frameworks\/audioplayers_darwin.framework\/audioplayers_darwin",
    "name" : "audioplayers_darwin",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4380508160,
    "CFBundleShortVersionString" : "1.3.8",
    "CFBundleIdentifier" : "org.cocoapods.background-fetch",
    "size" : 49152,
    "uuid" : "3082c5bb-a16c-31e6-8582-dcdb7654da58",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/24942F86-3060-4B2A-962B-60BD9614F1F4\/data\/Containers\/Bundle\/Application\/13FF1FEF-7257-49BB-8287-1FCD0810CD0E\/Runner.app\/Frameworks\/background_fetch.framework\/background_fetch",
    "name" : "background_fetch",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4389650432,
    "CFBundleShortVersionString" : "0.0.1",
    "CFBundleIdentifier" : "org.cocoapods.camera-avfoundation",
    "size" : 327680,
    "uuid" : "57779bd3-e5f9-39e2-8a94-6cc8e13eb4db",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/24942F86-3060-4B2A-962B-60BD9614F1F4\/data\/Containers\/Bundle\/Application\/13FF1FEF-7257-49BB-8287-1FCD0810CD0E\/Runner.app\/Frameworks\/camera_avfoundation.framework\/camera_avfoundation",
    "name" : "camera_avfoundation",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4385308672,
    "CFBundleShortVersionString" : "0.0.1",
    "CFBundleIdentifier" : "org.cocoapods.connectivity-plus",
    "size" : 49152,
    "uuid" : "a9267679-a863-3c9c-86b8-421fa69f8fdd",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/24942F86-3060-4B2A-962B-60BD9614F1F4\/data\/Containers\/Bundle\/Application\/13FF1FEF-7257-49BB-8287-1FCD0810CD0E\/Runner.app\/Frameworks\/connectivity_plus.framework\/connectivity_plus",
    "name" : "connectivity_plus",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4377018368,
    "CFBundleShortVersionString" : "0.0.1",
    "CFBundleIdentifier" : "org.cocoapods.cupertino-http",
    "size" : 65536,
    "uuid" : "c534a179-fcdb-3f80-b7d3-00ce32507584",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/24942F86-3060-4B2A-962B-60BD9614F1F4\/data\/Containers\/Bundle\/Application\/13FF1FEF-7257-49BB-8287-1FCD0810CD0E\/Runner.app\/Frameworks\/cupertino_http.framework\/cupertino_http",
    "name" : "cupertino_http",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4387094528,
    "CFBundleShortVersionString" : "0.0.1",
    "CFBundleIdentifier" : "org.cocoapods.file-picker",
    "size" : 81920,
    "uuid" : "c0502278-71ed-3e27-ade8-7c16e317672e",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/24942F86-3060-4B2A-962B-60BD9614F1F4\/data\/Containers\/Bundle\/Application\/13FF1FEF-7257-49BB-8287-1FCD0810CD0E\/Runner.app\/Frameworks\/file_picker.framework\/file_picker",
    "name" : "file_picker",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4390387712,
    "CFBundleShortVersionString" : "1.0.0",
    "CFBundleIdentifier" : "org.cocoapods.flutter-image-compress-common",
    "size" : 147456,
    "uuid" : "e0b45e4d-d6f6-3269-ba72-505c1df8f371",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/24942F86-3060-4B2A-962B-60BD9614F1F4\/data\/Containers\/Bundle\/Application\/13FF1FEF-7257-49BB-8287-1FCD0810CD0E\/Runner.app\/Frameworks\/flutter_image_compress_common.framework\/flutter_image_compress_common",
    "name" : "flutter_image_compress_common",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4387274752,
    "CFBundleShortVersionString" : "0.0.1",
    "CFBundleIdentifier" : "org.cocoapods.flutter-local-notifications",
    "size" : 65536,
    "uuid" : "a3de282d-4d89-34ed-b74c-6a0c45a157aa",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/24942F86-3060-4B2A-962B-60BD9614F1F4\/data\/Containers\/Bundle\/Application\/13FF1FEF-7257-49BB-8287-1FCD0810CD0E\/Runner.app\/Frameworks\/flutter_local_notifications.framework\/flutter_local_notifications",
    "name" : "flutter_local_notifications",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4388896768,
    "CFBundleShortVersionString" : "6.0.0",
    "CFBundleIdentifier" : "org.cocoapods.flutter-secure-storage",
    "size" : 65536,
    "uuid" : "d709d26f-6cb7-3452-91d8-5ab0d96da42e",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/24942F86-3060-4B2A-962B-60BD9614F1F4\/data\/Containers\/Bundle\/Application\/13FF1FEF-7257-49BB-8287-1FCD0810CD0E\/Runner.app\/Frameworks\/flutter_secure_storage.framework\/flutter_secure_storage",
    "name" : "flutter_secure_storage",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4389093376,
    "CFBundleShortVersionString" : "0.0.1",
    "CFBundleIdentifier" : "org.cocoapods.image-picker-ios",
    "size" : 81920,
    "uuid" : "d8c14994-b67e-3089-ad07-ef19b43fdc0b",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/24942F86-3060-4B2A-962B-60BD9614F1F4\/data\/Containers\/Bundle\/Application\/13FF1FEF-7257-49BB-8287-1FCD0810CD0E\/Runner.app\/Frameworks\/image_picker_ios.framework\/image_picker_ios",
    "name" : "image_picker_ios",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4397203456,
    "CFBundleShortVersionString" : "1.5.0",
    "CFBundleIdentifier" : "org.cocoapods.libwebp",
    "size" : 655360,
    "uuid" : "ac0f2aff-6f25-3923-951f-5204e2d38d98",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/24942F86-3060-4B2A-962B-60BD9614F1F4\/data\/Containers\/Bundle\/Application\/13FF1FEF-7257-49BB-8287-1FCD0810CD0E\/Runner.app\/Frameworks\/libwebp.framework\/libwebp",
    "name" : "libwebp",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4379639808,
    "CFBundleShortVersionString" : "3.30910.0",
    "CFBundleIdentifier" : "org.cocoapods.nanopb",
    "size" : 32768,
    "uuid" : "6762a519-8c26-374b-9732-572f22a294a8",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/24942F86-3060-4B2A-962B-60BD9614F1F4\/data\/Containers\/Bundle\/Application\/13FF1FEF-7257-49BB-8287-1FCD0810CD0E\/Runner.app\/Frameworks\/nanopb.framework\/nanopb",
    "name" : "nanopb",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4384047104,
    "CFBundleShortVersionString" : "0.0.1",
    "CFBundleIdentifier" : "org.cocoapods.objective-c",
    "size" : 32768,
    "uuid" : "0d8a2c56-fdbb-3c89-803a-4f866892c0b2",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/24942F86-3060-4B2A-962B-60BD9614F1F4\/data\/Containers\/Bundle\/Application\/13FF1FEF-7257-49BB-8287-1FCD0810CD0E\/Runner.app\/Frameworks\/objective_c.framework\/objective_c",
    "name" : "objective_c",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : **********,
    "CFBundleShortVersionString" : "0.0.1",
    "CFBundleIdentifier" : "org.cocoapods.path-provider-foundation",
    "size" : 49152,
    "uuid" : "ea818c3f-c528-3db6-9e33-cc9232a2234a",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/24942F86-3060-4B2A-962B-60BD9614F1F4\/data\/Containers\/Bundle\/Application\/13FF1FEF-7257-49BB-8287-1FCD0810CD0E\/Runner.app\/Frameworks\/path_provider_foundation.framework\/path_provider_foundation",
    "name" : "path_provider_foundation",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : **********,
    "CFBundleShortVersionString" : "0.0.1",
    "CFBundleIdentifier" : "org.cocoapods.posthog-flutter",
    "size" : 65536,
    "uuid" : "05eb2d96-d712-30ef-abfb-0c9e0bd2e106",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/24942F86-3060-4B2A-962B-60BD9614F1F4\/data\/Containers\/Bundle\/Application\/13FF1FEF-7257-49BB-8287-1FCD0810CD0E\/Runner.app\/Frameworks\/posthog_flutter.framework\/posthog_flutter",
    "name" : "posthog_flutter",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : **********,
    "CFBundleShortVersionString" : "1.0.0",
    "CFBundleIdentifier" : "org.cocoapods.record-ios",
    "size" : 147456,
    "uuid" : "4fb8fc2b-1094-37ca-aa9a-c48d9937c6de",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/24942F86-3060-4B2A-962B-60BD9614F1F4\/data\/Containers\/Bundle\/Application\/13FF1FEF-7257-49BB-8287-1FCD0810CD0E\/Runner.app\/Frameworks\/record_ios.framework\/record_ios",
    "name" : "record_ios",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4391567360,
    "CFBundleShortVersionString" : "0.0.1",
    "CFBundleIdentifier" : "org.cocoapods.shared-preferences-foundation",
    "size" : 81920,
    "uuid" : "7f787c28-8ab8-38d1-9f4a-9c0acc03d294",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/24942F86-3060-4B2A-962B-60BD9614F1F4\/data\/Containers\/Bundle\/Application\/13FF1FEF-7257-49BB-8287-1FCD0810CD0E\/Runner.app\/Frameworks\/shared_preferences_foundation.framework\/shared_preferences_foundation",
    "name" : "shared_preferences_foundation",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4398170112,
    "CFBundleShortVersionString" : "0.0.1",
    "CFBundleIdentifier" : "org.cocoapods.sign-in-with-apple",
    "size" : 81920,
    "uuid" : "6a97bc70-a8c9-3481-bc7f-0c94463d6fc7",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/24942F86-3060-4B2A-962B-60BD9614F1F4\/data\/Containers\/Bundle\/Application\/13FF1FEF-7257-49BB-8287-1FCD0810CD0E\/Runner.app\/Frameworks\/sign_in_with_apple.framework\/sign_in_with_apple",
    "name" : "sign_in_with_apple",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4398399488,
    "CFBundleShortVersionString" : "0.0.4",
    "CFBundleIdentifier" : "org.cocoapods.sqflite-darwin",
    "size" : 131072,
    "uuid" : "8089bff1-2204-3398-b695-4d26b27ed341",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/24942F86-3060-4B2A-962B-60BD9614F1F4\/data\/Containers\/Bundle\/Application\/13FF1FEF-7257-49BB-8287-1FCD0810CD0E\/Runner.app\/Frameworks\/sqflite_darwin.framework\/sqflite_darwin",
    "name" : "sqflite_darwin",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4398694400,
    "CFBundleShortVersionString" : "0.0.1",
    "CFBundleIdentifier" : "org.cocoapods.video-player-avfoundation",
    "size" : 98304,
    "uuid" : "c69d2e76-b993-3733-b43c-444bb697ef2b",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/24942F86-3060-4B2A-962B-60BD9614F1F4\/data\/Containers\/Bundle\/Application\/13FF1FEF-7257-49BB-8287-1FCD0810CD0E\/Runner.app\/Frameworks\/video_player_avfoundation.framework\/video_player_avfoundation",
    "name" : "video_player_avfoundation",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4400693248,
    "CFBundleShortVersionString" : "0.0.1",
    "CFBundleIdentifier" : "org.cocoapods.webview-flutter-wkwebview",
    "size" : 704512,
    "uuid" : "ec39c002-5a1d-3a12-a28b-6f9ab7cfcbb4",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/24942F86-3060-4B2A-962B-60BD9614F1F4\/data\/Containers\/Bundle\/Application\/13FF1FEF-7257-49BB-8287-1FCD0810CD0E\/Runner.app\/Frameworks\/webview_flutter_wkwebview.framework\/webview_flutter_wkwebview",
    "name" : "webview_flutter_wkwebview",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4399398912,
    "CFBundleShortVersionString" : "0.0.1",
    "CFBundleIdentifier" : "org.cocoapods.workmanager",
    "size" : 163840,
    "uuid" : "f1b859ba-2861-3133-a066-20a0754936f2",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/24942F86-3060-4B2A-962B-60BD9614F1F4\/data\/Containers\/Bundle\/Application\/13FF1FEF-7257-49BB-8287-1FCD0810CD0E\/Runner.app\/Frameworks\/workmanager.framework\/workmanager",
    "name" : "workmanager",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4467032064,
    "CFBundleShortVersionString" : "1.0",
    "CFBundleIdentifier" : "io.flutter.flutter",
    "size" : 35110912,
    "uuid" : "4c4c4445-5555-3144-a11f-921ceb984ad8",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/24942F86-3060-4B2A-962B-60BD9614F1F4\/data\/Containers\/Bundle\/Application\/13FF1FEF-7257-49BB-8287-1FCD0810CD0E\/Runner.app\/Frameworks\/Flutter.framework\/Flutter",
    "name" : "Flutter",
    "CFBundleVersion" : "1.0"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4380655616,
    "size" : 32768,
    "uuid" : "f77d8041-529c-3659-ac12-4821c1c301fc",
    "path" : "\/usr\/lib\/system\/libsystem_platform.dylib",
    "name" : "libsystem_platform.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4398923776,
    "size" : 245760,
    "uuid" : "f30ad29e-a6a0-3f43-8f6e-3770f040794d",
    "path" : "\/usr\/lib\/system\/libsystem_kernel.dylib",
    "name" : "libsystem_kernel.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4390797312,
    "size" : 65536,
    "uuid" : "7d5c0390-4e96-3458-ad9c-5c41b02beefd",
    "path" : "\/usr\/lib\/system\/libsystem_pthread.dylib",
    "name" : "libsystem_pthread.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4405067776,
    "size" : 49152,
    "uuid" : "56878cbd-4b61-3d67-a830-23a1b2beaf59",
    "path" : "\/Volumes\/VOLUME\/*\/libobjc-trampolines.dylib",
    "name" : "libobjc-trampolines.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 6443491328,
    "size" : 509852,
    "uuid" : "d8eab09a-74cb-346d-b14e-3187db1c40db",
    "path" : "\/Volumes\/VOLUME\/*\/libsystem_c.dylib",
    "name" : "libsystem_c.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 6445182976,
    "size" : 114688,
    "uuid" : "649076f2-9c2b-3e8e-833b-d246ac43869e",
    "path" : "\/Volumes\/VOLUME\/*\/libc++abi.dylib",
    "name" : "libc++abi.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 6442876928,
    "size" : 248096,
    "uuid" : "b10e226d-4fab-3450-9a4c-071d3d0edf3a",
    "path" : "\/Volumes\/VOLUME\/*\/libobjc.A.dylib",
    "name" : "libobjc.A.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 6446211072,
    "CFBundleShortVersionString" : "6.9",
    "CFBundleIdentifier" : "com.apple.CoreFoundation",
    "size" : 4292608,
    "uuid" : "ae27f481-c1fa-359c-b04c-af9cda7655ff",
    "path" : "\/Volumes\/VOLUME\/*\/CoreFoundation.framework\/CoreFoundation",
    "name" : "CoreFoundation",
    "CFBundleVersion" : "3502"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 6444003328,
    "size" : 281504,
    "uuid" : "990151a6-fd18-3496-84e3-f565307fbc2f",
    "path" : "\/Volumes\/VOLUME\/*\/libdispatch.dylib",
    "name" : "libdispatch.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 6727671808,
    "CFBundleShortVersionString" : "1.0",
    "CFBundleIdentifier" : "com.apple.GraphicsServices",
    "size" : 33280,
    "uuid" : "80b30bb2-e6e1-317e-b798-ea590de713a8",
    "path" : "\/Volumes\/VOLUME\/*\/GraphicsServices.framework\/GraphicsServices",
    "name" : "GraphicsServices",
    "CFBundleVersion" : "1.0"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 6524219392,
    "CFBundleShortVersionString" : "1.0",
    "CFBundleIdentifier" : "com.apple.UIKitCore",
    "size" : 30851776,
    "uuid" : "f5406608-aa34-30ba-8494-0a8b531792f5",
    "path" : "\/Volumes\/VOLUME\/*\/UIKitCore.framework\/UIKitCore",
    "name" : "UIKitCore",
    "CFBundleVersion" : "8506.1.101"
  },
  {
    "size" : 0,
    "source" : "A",
    "base" : 0,
    "uuid" : "00000000-0000-0000-0000-000000000000"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 6451023872,
    "CFBundleShortVersionString" : "6.9",
    "CFBundleIdentifier" : "com.apple.Foundation",
    "size" : 12254784,
    "uuid" : "48eb0271-c8d1-359a-bd56-bcf3e7e37dc5",
    "path" : "\/Volumes\/VOLUME\/*\/Foundation.framework\/Foundation",
    "name" : "Foundation",
    "CFBundleVersion" : "3502"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 7360225280,
    "CFBundleShortVersionString" : "1.0",
    "CFBundleIdentifier" : "com.apple.audio.caulk",
    "size" : 153056,
    "uuid" : "7f22c3c4-1a93-34dd-bf46-522f8d2a0e77",
    "path" : "\/Volumes\/VOLUME\/*\/caulk.framework\/caulk",
    "name" : "caulk"
  }
],
  "sharedCache" : {
  "base" : 6442450944,
  "size" : 3915218944,
  "uuid" : "197e44a5-4546-37f4-8533-db09ce21f1e6"
},
  "vmSummary" : "ReadOnly portion of Libraries: Total=1.6G resident=0K(0%) swapped_out_or_unallocated=1.6G(100%)\nWritable regions: Total=1.7G written=2056K(0%) resident=2056K(0%) swapped_out=0K(0%) unallocated=1.7G(100%)\n\n                                VIRTUAL   REGION \nREGION TYPE                        SIZE    COUNT (non-coalesced) \n===========                     =======  ======= \nActivity Tracing                   256K        1 \nColorSync                           64K        4 \nFoundation                          16K        1 \nIOSurface                           16K        1 \nKernel Alloc Once                   32K        1 \nMALLOC                             1.6G       48 \nMALLOC guard page                  192K       12 \nSTACK GUARD                       56.5M       35 \nStack                             33.0M       35 \nVM_ALLOCATE                      110.4M      312 \n__DATA                            33.3M      821 \n__DATA_CONST                      94.8M      839 \n__DATA_DIRTY                       139K       11 \n__FONT_DATA                        2352        1 \n__LINKEDIT                       709.8M       52 \n__OBJC_RO                         61.3M        1 \n__OBJC_RW                         2727K        1 \n__TEXT                           934.4M      854 \n__TPRO_CONST                       148K        2 \ndyld private memory                2.5G       14 \nmapped file                      138.1M       17 \npage table in kernel              2056K        1 \nshared memory                       48K        3 \n===========                     =======  ======= \nTOTAL                              6.2G     3067 \n",
  "legacyInfo" : {
  "threadTriggered" : {

  }
},
  "logWritingSignature" : "aac8724596e61c8adca25142c38e6d281eac1d9a",
  "trialInfo" : {
  "rollouts" : [
    {
      "rolloutId" : "645197bf528fbf3c3af54105",
      "factorPackIds" : [
        "663e65b4a1526e1ca0e288a1"
      ],
      "deploymentId" : 240000002
    },
    {
      "rolloutId" : "652eff3d1bce5442b8d753c9",
      "factorPackIds" : [

      ],
      "deploymentId" : 250000005
    }
  ],
  "experiments" : [
    {
      "treatmentId" : "cc637087-5f6a-4428-bea8-ad080e9a6232",
      "experimentId" : "68630692e1874a7adc676348",
      "deploymentId" : 500000001
    }
  ]
}
}

