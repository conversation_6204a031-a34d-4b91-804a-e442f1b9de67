-------------------------------------
Translated Report (Full Report Below)
-------------------------------------
Process:             Runner [80306]
Path:                /Users/<USER>/Library/Developer/CoreSimulator/Devices/24942F86-3060-4B2A-962B-60BD9614F1F4/data/Containers/Bundle/Application/13FF1FEF-7257-49BB-8287-1FCD0810CD0E/Runner.app/Runner
Identifier:          com.hopenapp.hopen
Version:             1.0.0 (1)
Code Type:           ARM-64 (Native)
Role:                Foreground
Parent Process:      launchd_sim [68304]
Coalition:           com.apple.CoreSimulator.SimDevice.24942F86-3060-4B2A-962B-60BD9614F1F4 [103843]
Responsible Process: SimulatorTrampoline [12609]
User ID:             501

Date/Time:           2025-07-19 22:20:39.2369 -0400
Launch Time:         2025-07-19 22:20:35.3977 -0400
Hardware Model:      MacBookPro18,3
OS Version:          macOS 26.0 (25A5306g)
Release Type:        User

Crash Reporter Key:  65553204-4A28-33BB-B7F3-8A48D7F2DE89
Incident Identifier: 87254F34-ED9B-4A2C-AA3A-72C851E54FF9

Sleep/Wake UUID:       3E9C9FEE-CE70-435F-BB7B-7D695E48AC2B

Time Awake Since Boot: 140000 seconds
Time Since Wake:       42472 seconds

System Integrity Protection: enabled

Triggered by Thread: 0

Exception Type:    EXC_CRASH (SIGABRT)
Exception Codes:   0x0000000000000000, 0x0000000000000000

Termination Reason:  Namespace SIGNAL, Code 6, Abort trap: 6
Terminating Process: Runner [80306]


Last Exception Backtrace:
0   CoreFoundation                	       0x1804c97c8 __exceptionPreprocess + 160
1   libobjc.A.dylib               	       0x1800937cc objc_exception_throw + 72
2   CoreFoundation                	       0x1804c96e4 -[NSException initWithCoder:] + 0
3   FirebaseCore                  	       0x10485532c +[FIRApp configure] + 120 (FIRApp.m:115)
4   Runner.debug.dylib            	       0x10475cc00 closure #2 in AppDelegate.application(_:didFinishLaunchingWithOptions:) + 148 (AppDelegate.swift:49)
5   Runner.debug.dylib            	       0x10475cf1c thunk for @escaping @callee_guaranteed () -> () + 48
6   libdispatch.dylib             	       0x18017c788 _dispatch_call_block_and_release + 24
7   libdispatch.dylib             	       0x180197278 _dispatch_client_callout + 12
8   libdispatch.dylib             	       0x1801b300c <deduplicated_symbol> + 24
9   libdispatch.dylib             	       0x18018c1c4 _dispatch_main_queue_drain + 1184
10  libdispatch.dylib             	       0x18018bd14 _dispatch_main_queue_callback_4CF + 40
11  CoreFoundation                	       0x180428e9c __CFRUNLOOP_IS_SERVICING_THE_MAIN_DISPATCH_QUEUE__ + 12
12  CoreFoundation                	       0x1804238a8 __CFRunLoopRun + 1920
13  CoreFoundation                	       0x180422cec CFRunLoopRunSpecific + 536
14  GraphicsServices              	       0x191004d00 GSEventRunModal + 164
15  UIKitCore                     	       0x185c597d4 -[UIApplication _run] + 796
16  UIKitCore                     	       0x185c5dba0 UIApplicationMain + 124
17  UIKitCore                     	       0x185023f1c 0x184dfb000 + 2264860
18  Runner.debug.dylib            	       0x104763fe8 static UIApplicationDelegate.main() + 128
19  Runner.debug.dylib            	       0x104763f58 static AppDelegate.$main() + 44
20  Runner.debug.dylib            	       0x1047648bc __debug_main_executable_dylib_entry_point + 28
21  ???                           	       0x1046a13d4 ???
22  dyld                          	       0x104574924 start + 6400

Thread 0 Crashed:
0   libsystem_kernel.dylib        	       0x105b0885c __pthread_kill + 8
1   libsystem_pthread.dylib       	       0x1053622ec pthread_kill + 264
2   libsystem_c.dylib             	       0x180171ea8 abort + 100
3   libc++abi.dylib               	       0x1802b0144 abort_message + 128
4   libc++abi.dylib               	       0x18029fe4c demangling_terminate_handler() + 296
5   libobjc.A.dylib               	       0x18006f220 _objc_terminate() + 124
6   libc++abi.dylib               	       0x1802af570 std::__terminate(void (*)()) + 12
7   libc++abi.dylib               	       0x1802b2498 __cxxabiv1::failed_throw(__cxxabiv1::__cxa_exception*) + 32
8   libc++abi.dylib               	       0x1802b2478 __cxa_throw + 88
9   libobjc.A.dylib               	       0x180093904 objc_exception_throw + 384
10  CoreFoundation                	       0x1804c96e4 +[NSException raise:format:] + 124
11  FirebaseCore                  	       0x10485532c +[FIRApp configure] + 120 (FIRApp.m:110)
12  Runner.debug.dylib            	       0x10475cc00 closure #2 in AppDelegate.application(_:didFinishLaunchingWithOptions:) + 148 (AppDelegate.swift:49)
13  Runner.debug.dylib            	       0x10475cf1c thunk for @escaping @callee_guaranteed () -> () + 48
14  libdispatch.dylib             	       0x18017c788 _dispatch_call_block_and_release + 24
15  libdispatch.dylib             	       0x180197278 _dispatch_client_callout + 12
16  libdispatch.dylib             	       0x1801b300c <deduplicated_symbol> + 24
17  libdispatch.dylib             	       0x18018c1c4 _dispatch_main_queue_drain + 1184
18  libdispatch.dylib             	       0x18018bd14 _dispatch_main_queue_callback_4CF + 40
19  CoreFoundation                	       0x180428e9c __CFRUNLOOP_IS_SERVICING_THE_MAIN_DISPATCH_QUEUE__ + 12
20  CoreFoundation                	       0x1804238a8 __CFRunLoopRun + 1920
21  CoreFoundation                	       0x180422cec CFRunLoopRunSpecific + 536
22  GraphicsServices              	       0x191004d00 GSEventRunModal + 164
23  UIKitCore                     	       0x185c597d4 -[UIApplication _run] + 796
24  UIKitCore                     	       0x185c5dba0 UIApplicationMain + 124
25  UIKitCore                     	       0x185023f1c 0x184dfb000 + 2264860
26  Runner.debug.dylib            	       0x104763fe8 static UIApplicationDelegate.main() + 128
27  Runner.debug.dylib            	       0x104763f58 static AppDelegate.$main() + 44
28  Runner.debug.dylib            	       0x1047648bc __debug_main_executable_dylib_entry_point + 28
29  ???                           	       0x1046a13d4 ???
30  dyld                          	       0x104574924 start + 6400

Thread 1:
0   libsystem_pthread.dylib       	       0x10535d984 start_wqthread + 0

Thread 2:
0   libsystem_pthread.dylib       	       0x10535d984 start_wqthread + 0

Thread 3:
0   libsystem_pthread.dylib       	       0x10535d984 start_wqthread + 0

Thread 4:
0   libsystem_pthread.dylib       	       0x10535d984 start_wqthread + 0

Thread 5:: com.apple.uikit.eventfetch-thread
0   libsystem_kernel.dylib        	       0x105b00b70 mach_msg2_trap + 8
1   libsystem_kernel.dylib        	       0x105b1190c mach_msg2_internal + 72
2   libsystem_kernel.dylib        	       0x105b08c10 mach_msg_overwrite + 480
3   libsystem_kernel.dylib        	       0x105b00ee4 mach_msg + 20
4   CoreFoundation                	       0x180428bc4 __CFRunLoopServiceMachPort + 156
5   CoreFoundation                	       0x1804235a4 __CFRunLoopRun + 1148
6   CoreFoundation                	       0x180422cec CFRunLoopRunSpecific + 536
7   Foundation                    	       0x180f0e38c -[NSRunLoop(NSRunLoop) runMode:beforeDate:] + 208
8   Foundation                    	       0x180f0e5ac -[NSRunLoop(NSRunLoop) runUntilDate:] + 60
9   UIKitCore                     	       0x185d07390 -[UIEventFetcher threadMain] + 408
10  Foundation                    	       0x180f35148 __NSThread__start__ + 716
11  libsystem_pthread.dylib       	       0x1053625f0 _pthread_start + 104
12  libsystem_pthread.dylib       	       0x10535d998 thread_start + 8

Thread 6:
0   libsystem_pthread.dylib       	       0x10535d984 start_wqthread + 0

Thread 7:
0   libsystem_pthread.dylib       	       0x10535d984 start_wqthread + 0

Thread 8:
0   libsystem_pthread.dylib       	       0x10535d984 start_wqthread + 0

Thread 9:
0   libsystem_kernel.dylib        	       0x105b00b70 mach_msg2_trap + 8
1   libsystem_kernel.dylib        	       0x105b1190c mach_msg2_internal + 72
2   libsystem_kernel.dylib        	       0x105b08c10 mach_msg_overwrite + 480
3   libsystem_kernel.dylib        	       0x105b00ee4 mach_msg + 20
4   libdispatch.dylib             	       0x1801989b0 _dispatch_mach_send_and_wait_for_reply + 564
5   libdispatch.dylib             	       0x180198d4c dispatch_mach_send_with_result_and_wait_for_reply + 56
6   libxpc.dylib                  	       0x1800cd450 xpc_connection_send_message_with_reply_sync + 252
7   MTLSimDriver                  	       0x22ccc8560 sendXPCMessageWithReplySync(NSObject<OS_xpc_object>*, NSObject<OS_xpc_object>*) + 48
8   MTLSimDriver                  	       0x22ccc7a78 dispatchOperationWihReply(NSObject<OS_xpc_object>*, NSObject<OS_dispatch_data>*, unsigned long long, void (NSObject<OS_xpc_object>*) block_pointer, void (void const*, unsigned long) block_pointer) + 156
9   MTLSimDriver                  	       0x22cccb3c8 -[MTLSimDevice newRenderPipelineWithDescriptor:vertexVariant:fragmentVariant:errorMessage:] + 328
10  Metal                         	       0x19fbe501c newRenderPipeline(MTLProgramObject*, MTLFunctionVariant*, MTLProgramObject*, MTLFunctionVariant*, MTLProgramObject*, MTLFunctionVariant*, NSObject<OS_dispatch_data>*, MTLProgramObject*, MTLFunctionVariant*, NSObject<OS_dispatch_data>*, _MTLDevice*, NSObject<OS_dispatch_data>*, MTLRenderPipelineDescriptor*, unsigned long, unsigned long, MTLRenderPipelineReflection**, NSMutableDictionary*, NSObject<OS_dispatch_data>*, NSObject<OS_dispatch_data>*, void (id<MTLRenderPipelineState>, MTLRenderPipelineReflection*, NSError*) block_pointer, NSError*) + 140
11  Metal                         	       0x19fbe8b94 __266-[MTLCompiler createVertexStageAndLinkPipelineWithFragment:fragmentVariant:vertexFunction:serializedVertexDescriptor:descriptor:airDescriptor:destinationArchive:options:reflection:compileStatistics:fragmentCompileTimeData:pipelineArchiverId:error:completionHandler:]_block_invoke_5 + 264
12  libdispatch.dylib             	       0x18017c788 _dispatch_call_block_and_release + 24
13  libdispatch.dylib             	       0x180197278 _dispatch_client_callout + 12
14  libdispatch.dylib             	       0x180185ad0 _dispatch_lane_serial_drain + 984
15  libdispatch.dylib             	       0x180186590 _dispatch_lane_invoke + 396
16  libdispatch.dylib             	       0x180191380 _dispatch_root_queue_drain_deferred_wlh + 288
17  libdispatch.dylib             	       0x1801909f4 _dispatch_workloop_worker_thread + 440
18  libsystem_pthread.dylib       	       0x10535ebd0 _pthread_wqthread + 288
19  libsystem_pthread.dylib       	       0x10535d98c start_wqthread + 8

Thread 10:
0   libsystem_pthread.dylib       	       0x10535d984 start_wqthread + 0

Thread 11:
0   libsystem_pthread.dylib       	       0x10535d984 start_wqthread + 0

Thread 12:
0   libsystem_pthread.dylib       	       0x10535d984 start_wqthread + 0

Thread 13:
0   libsystem_pthread.dylib       	       0x10535d984 start_wqthread + 0

Thread 14:
0   libsystem_pthread.dylib       	       0x10535d984 start_wqthread + 0

Thread 15:
0   libsystem_pthread.dylib       	       0x10535d984 start_wqthread + 0

Thread 16:
0   libsystem_pthread.dylib       	       0x10535d984 start_wqthread + 0

Thread 17:
0   libsystem_pthread.dylib       	       0x10535d984 start_wqthread + 0

Thread 18:
0   libsystem_pthread.dylib       	       0x10535d984 start_wqthread + 0

Thread 19:
0   libsystem_pthread.dylib       	       0x10535d984 start_wqthread + 0

Thread 20:
0   libsystem_pthread.dylib       	       0x10535d984 start_wqthread + 0

Thread 21:: io.flutter.1.raster
0   libsystem_kernel.dylib        	       0x105b00b70 mach_msg2_trap + 8
1   libsystem_kernel.dylib        	       0x105b1190c mach_msg2_internal + 72
2   libsystem_kernel.dylib        	       0x105b08c10 mach_msg_overwrite + 480
3   libsystem_kernel.dylib        	       0x105b00ee4 mach_msg + 20
4   CoreFoundation                	       0x180428bc4 __CFRunLoopServiceMachPort + 156
5   CoreFoundation                	       0x1804235a4 __CFRunLoopRun + 1148
6   CoreFoundation                	       0x180422cec CFRunLoopRunSpecific + 536
7   Flutter                       	       0x109c4a438 fml::MessageLoopDarwin::Run() + 88
8   Flutter                       	       0x109c43804 fml::MessageLoopImpl::DoRun() + 40
9   Flutter                       	       0x109c490d0 std::_fl::__function::__func<fml::Thread::Thread(std::_fl::function<void (fml::Thread::ThreadConfig const&)> const&, fml::Thread::ThreadConfig const&)::$_0, std::_fl::allocator<fml::Thread::Thread(std::_fl::function<void (fml::Thread::ThreadConfig const&)> const&, fml::Thread::ThreadConfig const&)::$_0>, void ()>::operator()() + 184
10  Flutter                       	       0x109c48da4 fml::ThreadHandle::ThreadHandle(std::_fl::function<void ()>&&)::$_0::__invoke(void*) + 36
11  libsystem_pthread.dylib       	       0x1053625f0 _pthread_start + 104
12  libsystem_pthread.dylib       	       0x10535d998 thread_start + 8

Thread 22:: io.flutter.1.io
0   libsystem_kernel.dylib        	       0x105b00b70 mach_msg2_trap + 8
1   libsystem_kernel.dylib        	       0x105b1190c mach_msg2_internal + 72
2   libsystem_kernel.dylib        	       0x105b08c10 mach_msg_overwrite + 480
3   libsystem_kernel.dylib        	       0x105b00ee4 mach_msg + 20
4   CoreFoundation                	       0x180428bc4 __CFRunLoopServiceMachPort + 156
5   CoreFoundation                	       0x1804235a4 __CFRunLoopRun + 1148
6   CoreFoundation                	       0x180422cec CFRunLoopRunSpecific + 536
7   Flutter                       	       0x109c4a438 fml::MessageLoopDarwin::Run() + 88
8   Flutter                       	       0x109c43804 fml::MessageLoopImpl::DoRun() + 40
9   Flutter                       	       0x109c490d0 std::_fl::__function::__func<fml::Thread::Thread(std::_fl::function<void (fml::Thread::ThreadConfig const&)> const&, fml::Thread::ThreadConfig const&)::$_0, std::_fl::allocator<fml::Thread::Thread(std::_fl::function<void (fml::Thread::ThreadConfig const&)> const&, fml::Thread::ThreadConfig const&)::$_0>, void ()>::operator()() + 184
10  Flutter                       	       0x109c48da4 fml::ThreadHandle::ThreadHandle(std::_fl::function<void ()>&&)::$_0::__invoke(void*) + 36
11  libsystem_pthread.dylib       	       0x1053625f0 _pthread_start + 104
12  libsystem_pthread.dylib       	       0x10535d998 thread_start + 8

Thread 23:: io.flutter.1.profiler
0   libsystem_kernel.dylib        	       0x105b00b70 mach_msg2_trap + 8
1   libsystem_kernel.dylib        	       0x105b1190c mach_msg2_internal + 72
2   libsystem_kernel.dylib        	       0x105b08c10 mach_msg_overwrite + 480
3   libsystem_kernel.dylib        	       0x105b00ee4 mach_msg + 20
4   CoreFoundation                	       0x180428bc4 __CFRunLoopServiceMachPort + 156
5   CoreFoundation                	       0x1804235a4 __CFRunLoopRun + 1148
6   CoreFoundation                	       0x180422cec CFRunLoopRunSpecific + 536
7   Flutter                       	       0x109c4a438 fml::MessageLoopDarwin::Run() + 88
8   Flutter                       	       0x109c43804 fml::MessageLoopImpl::DoRun() + 40
9   Flutter                       	       0x109c490d0 std::_fl::__function::__func<fml::Thread::Thread(std::_fl::function<void (fml::Thread::ThreadConfig const&)> const&, fml::Thread::ThreadConfig const&)::$_0, std::_fl::allocator<fml::Thread::Thread(std::_fl::function<void (fml::Thread::ThreadConfig const&)> const&, fml::Thread::ThreadConfig const&)::$_0>, void ()>::operator()() + 184
10  Flutter                       	       0x109c48da4 fml::ThreadHandle::ThreadHandle(std::_fl::function<void ()>&&)::$_0::__invoke(void*) + 36
11  libsystem_pthread.dylib       	       0x1053625f0 _pthread_start + 104
12  libsystem_pthread.dylib       	       0x10535d998 thread_start + 8

Thread 24:: io.worker.1
0   libsystem_kernel.dylib        	       0x105b04020 __psynch_cvwait + 8
1   libsystem_pthread.dylib       	       0x105362ab8 _pthread_cond_wait + 976
2   Flutter                       	       0x109c20958 std::_fl::condition_variable::wait(std::_fl::unique_lock<std::_fl::mutex>&) + 24
3   Flutter                       	       0x109c3fe34 fml::ConcurrentMessageLoop::WorkerMain() + 140
4   Flutter                       	       0x109c40760 void* std::_fl::__thread_proxy[abi:nn210000]<std::_fl::tuple<std::_fl::unique_ptr<std::_fl::__thread_struct, std::_fl::default_delete<std::_fl::__thread_struct>>, fml::ConcurrentMessageLoop::ConcurrentMessageLoop(unsigned long)::$_0>>(void*) + 212
5   libsystem_pthread.dylib       	       0x1053625f0 _pthread_start + 104
6   libsystem_pthread.dylib       	       0x10535d998 thread_start + 8

Thread 25:: io.worker.2
0   libsystem_kernel.dylib        	       0x105b04020 __psynch_cvwait + 8
1   libsystem_pthread.dylib       	       0x105362ab8 _pthread_cond_wait + 976
2   Flutter                       	       0x109c20958 std::_fl::condition_variable::wait(std::_fl::unique_lock<std::_fl::mutex>&) + 24
3   Flutter                       	       0x109c3fe34 fml::ConcurrentMessageLoop::WorkerMain() + 140
4   Flutter                       	       0x109c40760 void* std::_fl::__thread_proxy[abi:nn210000]<std::_fl::tuple<std::_fl::unique_ptr<std::_fl::__thread_struct, std::_fl::default_delete<std::_fl::__thread_struct>>, fml::ConcurrentMessageLoop::ConcurrentMessageLoop(unsigned long)::$_0>>(void*) + 212
5   libsystem_pthread.dylib       	       0x1053625f0 _pthread_start + 104
6   libsystem_pthread.dylib       	       0x10535d998 thread_start + 8

Thread 26:: io.worker.3
0   libsystem_kernel.dylib        	       0x105b04020 __psynch_cvwait + 8
1   libsystem_pthread.dylib       	       0x105362ab8 _pthread_cond_wait + 976
2   Flutter                       	       0x109c20958 std::_fl::condition_variable::wait(std::_fl::unique_lock<std::_fl::mutex>&) + 24
3   Flutter                       	       0x109c3fe34 fml::ConcurrentMessageLoop::WorkerMain() + 140
4   Flutter                       	       0x109c40760 void* std::_fl::__thread_proxy[abi:nn210000]<std::_fl::tuple<std::_fl::unique_ptr<std::_fl::__thread_struct, std::_fl::default_delete<std::_fl::__thread_struct>>, fml::ConcurrentMessageLoop::ConcurrentMessageLoop(unsigned long)::$_0>>(void*) + 212
5   libsystem_pthread.dylib       	       0x1053625f0 _pthread_start + 104
6   libsystem_pthread.dylib       	       0x10535d998 thread_start + 8

Thread 27:: io.worker.4
0   libsystem_kernel.dylib        	       0x105b04020 __psynch_cvwait + 8
1   libsystem_pthread.dylib       	       0x105362ab8 _pthread_cond_wait + 976
2   Flutter                       	       0x109c20958 std::_fl::condition_variable::wait(std::_fl::unique_lock<std::_fl::mutex>&) + 24
3   Flutter                       	       0x109c3fe34 fml::ConcurrentMessageLoop::WorkerMain() + 140
4   Flutter                       	       0x109c40760 void* std::_fl::__thread_proxy[abi:nn210000]<std::_fl::tuple<std::_fl::unique_ptr<std::_fl::__thread_struct, std::_fl::default_delete<std::_fl::__thread_struct>>, fml::ConcurrentMessageLoop::ConcurrentMessageLoop(unsigned long)::$_0>>(void*) + 212
5   libsystem_pthread.dylib       	       0x1053625f0 _pthread_start + 104
6   libsystem_pthread.dylib       	       0x10535d998 thread_start + 8

Thread 28:: dart:io EventHandler
0   libsystem_kernel.dylib        	       0x105b0665c kevent + 8
1   Flutter                       	       0x10a08ddf8 dart::bin::EventHandlerImplementation::EventHandlerEntry(unsigned long) + 300
2   Flutter                       	       0x10a0a9f50 dart::bin::ThreadStart(void*) + 88
3   libsystem_pthread.dylib       	       0x1053625f0 _pthread_start + 104
4   libsystem_pthread.dylib       	       0x10535d998 thread_start + 8

Thread 29:: Dart Profiler ThreadInterrupter
0   libsystem_kernel.dylib        	       0x105b04020 __psynch_cvwait + 8
1   libsystem_pthread.dylib       	       0x105362ab8 _pthread_cond_wait + 976
2   Flutter                       	       0x10a0f5658 dart::ConditionVariable::WaitMicros(dart::Mutex*, long long) + 128
3   Flutter                       	       0x10a2525f4 dart::ThreadInterrupter::ThreadMain(unsigned long) + 328
4   Flutter                       	       0x10a20a024 dart::ThreadStart(void*) + 204
5   libsystem_pthread.dylib       	       0x1053625f0 _pthread_start + 104
6   libsystem_pthread.dylib       	       0x10535d998 thread_start + 8

Thread 30:: Dart Profiler SampleBlockProcessor
0   libsystem_kernel.dylib        	       0x105b04020 __psynch_cvwait + 8
1   libsystem_pthread.dylib       	       0x105362ae4 _pthread_cond_wait + 1020
2   Flutter                       	       0x10a0f5648 dart::ConditionVariable::WaitMicros(dart::Mutex*, long long) + 112
3   Flutter                       	       0x10a20eb6c dart::SampleBlockProcessor::ThreadMain(unsigned long) + 292
4   Flutter                       	       0x10a20a024 dart::ThreadStart(void*) + 204
5   libsystem_pthread.dylib       	       0x1053625f0 _pthread_start + 104
6   libsystem_pthread.dylib       	       0x10535d998 thread_start + 8

Thread 31:: DartWorker
0   libsystem_kernel.dylib        	       0x105b04020 __psynch_cvwait + 8
1   libsystem_pthread.dylib       	       0x105362ae4 _pthread_cond_wait + 1020
2   Flutter                       	       0x10a0f5648 dart::ConditionVariable::WaitMicros(dart::Mutex*, long long) + 112
3   Flutter                       	       0x10a25336c dart::ThreadPool::WorkerLoop(dart::ThreadPool::Worker*) + 508
4   Flutter                       	       0x10a2534c4 dart::ThreadPool::Worker::Main(unsigned long) + 116
5   Flutter                       	       0x10a20a024 dart::ThreadStart(void*) + 204
6   libsystem_pthread.dylib       	       0x1053625f0 _pthread_start + 104
7   libsystem_pthread.dylib       	       0x10535d998 thread_start + 8

Thread 32:: DartWorker
0   libsystem_kernel.dylib        	       0x105b04020 __psynch_cvwait + 8
1   libsystem_pthread.dylib       	       0x105362ae4 _pthread_cond_wait + 1020
2   Flutter                       	       0x10a0f5648 dart::ConditionVariable::WaitMicros(dart::Mutex*, long long) + 112
3   Flutter                       	       0x10a25336c dart::ThreadPool::WorkerLoop(dart::ThreadPool::Worker*) + 508
4   Flutter                       	       0x10a2534c4 dart::ThreadPool::Worker::Main(unsigned long) + 116
5   Flutter                       	       0x10a20a024 dart::ThreadStart(void*) + 204
6   libsystem_pthread.dylib       	       0x1053625f0 _pthread_start + 104
7   libsystem_pthread.dylib       	       0x10535d998 thread_start + 8

Thread 33:: DartWorker
0   libsystem_kernel.dylib        	       0x105b04020 __psynch_cvwait + 8
1   libsystem_pthread.dylib       	       0x105362ae4 _pthread_cond_wait + 1020
2   Flutter                       	       0x10a0f5648 dart::ConditionVariable::WaitMicros(dart::Mutex*, long long) + 112
3   Flutter                       	       0x10a15388c dart::MutatorThreadPool::OnEnterIdleLocked(dart::MutexLocker*, dart::ThreadPool::Worker*) + 152
4   Flutter                       	       0x10a2531ec dart::ThreadPool::WorkerLoop(dart::ThreadPool::Worker*) + 124
5   Flutter                       	       0x10a2534c4 dart::ThreadPool::Worker::Main(unsigned long) + 116
6   Flutter                       	       0x10a20a024 dart::ThreadStart(void*) + 204
7   libsystem_pthread.dylib       	       0x1053625f0 _pthread_start + 104
8   libsystem_pthread.dylib       	       0x10535d998 thread_start + 8

Thread 34:: DartWorker
0   libsystem_kernel.dylib        	       0x105b04020 __psynch_cvwait + 8
1   libsystem_pthread.dylib       	       0x105362ae4 _pthread_cond_wait + 1020
2   Flutter                       	       0x10a0f5648 dart::ConditionVariable::WaitMicros(dart::Mutex*, long long) + 112
3   Flutter                       	       0x10a25336c dart::ThreadPool::WorkerLoop(dart::ThreadPool::Worker*) + 508
4   Flutter                       	       0x10a2534c4 dart::ThreadPool::Worker::Main(unsigned long) + 116
5   Flutter                       	       0x10a20a024 dart::ThreadStart(void*) + 204
6   libsystem_pthread.dylib       	       0x1053625f0 _pthread_start + 104
7   libsystem_pthread.dylib       	       0x10535d998 thread_start + 8

Thread 35:: caulk.messenger.shared:17
0   libsystem_kernel.dylib        	       0x105b00aec semaphore_wait_trap + 8
1   caulk                         	       0x1b6b51b3c caulk::semaphore::timed_wait(double) + 220
2   caulk                         	       0x1b6b58c40 caulk::concurrent::details::worker_thread::run() + 28
3   caulk                         	       0x1b6b58cb4 void* caulk::thread_proxy<std::__1::tuple<caulk::thread::attributes, void (caulk::concurrent::details::worker_thread::*)(), std::__1::tuple<caulk::concurrent::details::worker_thread*>>>(void*) + 48
4   libsystem_pthread.dylib       	       0x1053625f0 _pthread_start + 104
5   libsystem_pthread.dylib       	       0x10535d998 thread_start + 8

Thread 36:: caulk.messenger.shared:high
0   libsystem_kernel.dylib        	       0x105b00aec semaphore_wait_trap + 8
1   caulk                         	       0x1b6b51b3c caulk::semaphore::timed_wait(double) + 220
2   caulk                         	       0x1b6b58c40 caulk::concurrent::details::worker_thread::run() + 28
3   caulk                         	       0x1b6b58cb4 void* caulk::thread_proxy<std::__1::tuple<caulk::thread::attributes, void (caulk::concurrent::details::worker_thread::*)(), std::__1::tuple<caulk::concurrent::details::worker_thread*>>>(void*) + 48
4   libsystem_pthread.dylib       	       0x1053625f0 _pthread_start + 104
5   libsystem_pthread.dylib       	       0x10535d998 thread_start + 8

Thread 37:: DartWorker
0   libsystem_kernel.dylib        	       0x105b04020 __psynch_cvwait + 8
1   libsystem_pthread.dylib       	       0x105362ae4 _pthread_cond_wait + 1020
2   Flutter                       	       0x10a0f5648 dart::ConditionVariable::WaitMicros(dart::Mutex*, long long) + 112
3   Flutter                       	       0x10a25336c dart::ThreadPool::WorkerLoop(dart::ThreadPool::Worker*) + 508
4   Flutter                       	       0x10a2534c4 dart::ThreadPool::Worker::Main(unsigned long) + 116
5   Flutter                       	       0x10a20a024 dart::ThreadStart(void*) + 204
6   libsystem_pthread.dylib       	       0x1053625f0 _pthread_start + 104
7   libsystem_pthread.dylib       	       0x10535d998 thread_start + 8


Thread 0 crashed with ARM Thread State (64-bit):
    x0: 0x0000000000000000   x1: 0x0000000000000000   x2: 0x0000000000000000   x3: 0x0000000000000000
    x4: 0x00000001802b3e9b   x5: 0x000000016ba0fa90   x6: 0x000000000000006e   x7: 0x0000000000000000
    x8: 0x0000000104619e40   x9: 0x35f4ce91b837f32a  x10: 0x0000000000000051  x11: 0x000000000000000b
   x12: 0x000000000000000b  x13: 0x00000001806e8af6  x14: 0x00000000000007fb  x15: 0x00000000c780603e
   x16: 0x0000000000000148  x17: 0x00000000c7a05844  x18: 0x0000000000000000  x19: 0x0000000000000006
   x20: 0x0000000000000103  x21: 0x0000000104619f20  x22: 0x00000001f2ca9000  x23: 0x00000001f2ca6ac0
   x24: 0x00000001f2ca6ac0  x25: 0x0000600001750480  x26: 0x0000600002c30200  x27: 0x0000000000000000
   x28: 0x0000000000000114   fp: 0x000000016ba0fa00   lr: 0x00000001053622ec
    sp: 0x000000016ba0f9e0   pc: 0x0000000105b0885c cpsr: 0x40001000
   far: 0x0000000000000000  esr: 0x56000080 (PC alignment)

Binary Images:
       0x10456c000 -        0x10460bfff dyld (*) <ff357121-622e-3ea0-9ef0-8d706f44dc1a> /usr/lib/dyld
       0x1043ec000 -        0x1043effff com.hopenapp.hopen (1.0.0) <caf7731a-2c8a-3262-ac04-f72f06f73eab> /Users/<USER>/Library/Developer/CoreSimulator/Devices/24942F86-3060-4B2A-962B-60BD9614F1F4/data/Containers/Bundle/Application/13FF1FEF-7257-49BB-8287-1FCD0810CD0E/Runner.app/Runner
       0x104758000 -        0x1047dbfff Runner.debug.dylib (*) <c14c9eb6-9864-3bfd-b8df-604b8f776e52> /Users/<USER>/Library/Developer/CoreSimulator/Devices/24942F86-3060-4B2A-962B-60BD9614F1F4/data/Containers/Bundle/Application/13FF1FEF-7257-49BB-8287-1FCD0810CD0E/Runner.app/Runner.debug.dylib
       0x104464000 -        0x10448bfff org.cocoapods.AppAuth (1.7.6) <cd329467-4e64-33fd-8f81-c140b9c607a9> /Users/<USER>/Library/Developer/CoreSimulator/Devices/24942F86-3060-4B2A-962B-60BD9614F1F4/data/Containers/Bundle/Application/13FF1FEF-7257-49BB-8287-1FCD0810CD0E/Runner.app/Frameworks/AppAuth.framework/AppAuth
       0x1044bc000 -        0x1044dffff org.cocoapods.AppCheckCore (11.2.0) <31ce7028-5a68-39f1-a840-891714831d69> /Users/<USER>/Library/Developer/CoreSimulator/Devices/24942F86-3060-4B2A-962B-60BD9614F1F4/data/Containers/Bundle/Application/13FF1FEF-7257-49BB-8287-1FCD0810CD0E/Runner.app/Frameworks/AppCheckCore.framework/AppCheckCore
       0x1049d4000 -        0x104a6ffff org.cocoapods.DKImagePickerController (4.3.9) <03fcf312-d00e-3900-9646-92f66cbdaf51> /Users/<USER>/Library/Developer/CoreSimulator/Devices/24942F86-3060-4B2A-962B-60BD9614F1F4/data/Containers/Bundle/Application/13FF1FEF-7257-49BB-8287-1FCD0810CD0E/Runner.app/Frameworks/DKImagePickerController.framework/DKImagePickerController
       0x104b54000 -        0x104bdbfff org.cocoapods.DKPhotoGallery (0.0.19) <9cc3f835-0645-3b4c-962e-78404b6674c6> /Users/<USER>/Library/Developer/CoreSimulator/Devices/24942F86-3060-4B2A-962B-60BD9614F1F4/data/Containers/Bundle/Application/13FF1FEF-7257-49BB-8287-1FCD0810CD0E/Runner.app/Frameworks/DKPhotoGallery.framework/DKPhotoGallery
       0x104514000 -        0x104527fff org.cocoapods.FBLPromises (2.4.0) <962c6d34-cde2-3ecf-ba9b-3af78f34ab5a> /Users/<USER>/Library/Developer/CoreSimulator/Devices/24942F86-3060-4B2A-962B-60BD9614F1F4/data/Containers/Bundle/Application/13FF1FEF-7257-49BB-8287-1FCD0810CD0E/Runner.app/Frameworks/FBLPromises.framework/FBLPromises
       0x104854000 -        0x104867fff org.cocoapods.FirebaseCore (11.15.0) <5e0d33ee-5834-3d02-972b-27a987c049e3> /Users/<USER>/Library/Developer/CoreSimulator/Devices/24942F86-3060-4B2A-962B-60BD9614F1F4/data/Containers/Bundle/Application/13FF1FEF-7257-49BB-8287-1FCD0810CD0E/Runner.app/Frameworks/FirebaseCore.framework/FirebaseCore
       0x104904000 -        0x10492bfff org.cocoapods.FirebaseCoreInternal (11.15.0) <29efc00f-307c-3f23-80fa-534b89bd3c78> /Users/<USER>/Library/Developer/CoreSimulator/Devices/24942F86-3060-4B2A-962B-60BD9614F1F4/data/Containers/Bundle/Application/13FF1FEF-7257-49BB-8287-1FCD0810CD0E/Runner.app/Frameworks/FirebaseCoreInternal.framework/FirebaseCoreInternal
       0x104888000 -        0x1048a3fff org.cocoapods.FirebaseInstallations (11.15.0) <d9fbd69a-5b18-38a7-adc4-499fc338c58c> /Users/<USER>/Library/Developer/CoreSimulator/Devices/24942F86-3060-4B2A-962B-60BD9614F1F4/data/Containers/Bundle/Application/13FF1FEF-7257-49BB-8287-1FCD0810CD0E/Runner.app/Frameworks/FirebaseInstallations.framework/FirebaseInstallations
       0x104d24000 -        0x104d6bfff org.cocoapods.FirebaseMessaging (11.15.0) <2decb982-fd1b-3698-ade5-50d8b05739d2> /Users/<USER>/Library/Developer/CoreSimulator/Devices/24942F86-3060-4B2A-962B-60BD9614F1F4/data/Containers/Bundle/Application/13FF1FEF-7257-49BB-8287-1FCD0810CD0E/Runner.app/Frameworks/FirebaseMessaging.framework/FirebaseMessaging
       0x104408000 -        0x10442bfff org.cocoapods.GTMAppAuth (4.1.1) <b56e79ea-28fb-3a59-b431-e8fc09b71b48> /Users/<USER>/Library/Developer/CoreSimulator/Devices/24942F86-3060-4B2A-962B-60BD9614F1F4/data/Containers/Bundle/Application/13FF1FEF-7257-49BB-8287-1FCD0810CD0E/Runner.app/Frameworks/GTMAppAuth.framework/GTMAppAuth
       0x104e54000 -        0x104eabfff org.cocoapods.GTMSessionFetcher (3.5.0) <205fdd37-40d9-38fe-b575-614ac31c7140> /Users/<USER>/Library/Developer/CoreSimulator/Devices/24942F86-3060-4B2A-962B-60BD9614F1F4/data/Containers/Bundle/Application/13FF1FEF-7257-49BB-8287-1FCD0810CD0E/Runner.app/Frameworks/GTMSessionFetcher.framework/GTMSessionFetcher
       0x104db4000 -        0x104de7fff org.cocoapods.GoogleDataTransport (10.1.0) <a5c335de-684e-3b83-ba64-e21e447ea889> /Users/<USER>/Library/Developer/CoreSimulator/Devices/24942F86-3060-4B2A-962B-60BD9614F1F4/data/Containers/Bundle/Application/13FF1FEF-7257-49BB-8287-1FCD0810CD0E/Runner.app/Frameworks/GoogleDataTransport.framework/GoogleDataTransport
       0x104c90000 -        0x104cb3fff org.cocoapods.GoogleSignIn (8.0.0) <55fb2deb-d907-305e-a508-ee748f35ff18> /Users/<USER>/Library/Developer/CoreSimulator/Devices/24942F86-3060-4B2A-962B-60BD9614F1F4/data/Containers/Bundle/Application/13FF1FEF-7257-49BB-8287-1FCD0810CD0E/Runner.app/Frameworks/GoogleSignIn.framework/GoogleSignIn
       0x104ef0000 -        0x104f13fff org.cocoapods.GoogleUtilities (8.1.0) <579d30f5-873d-3cac-afaa-2ef68808afd3> /Users/<USER>/Library/Developer/CoreSimulator/Devices/24942F86-3060-4B2A-962B-60BD9614F1F4/data/Containers/Bundle/Application/13FF1FEF-7257-49BB-8287-1FCD0810CD0E/Runner.app/Frameworks/GoogleUtilities.framework/GoogleUtilities
       0x1048cc000 -        0x1048e7fff org.cocoapods.Mantle (2.2.0) <6b831635-7679-349a-8447-49703eebba25> /Users/<USER>/Library/Developer/CoreSimulator/Devices/24942F86-3060-4B2A-962B-60BD9614F1F4/data/Containers/Bundle/Application/13FF1FEF-7257-49BB-8287-1FCD0810CD0E/Runner.app/Frameworks/Mantle.framework/Mantle
       0x10542c000 -        0x10565ffff org.cocoapods.PostHog (3.29.0) <780581b9-7198-311c-820d-77468c8d52c9> /Users/<USER>/Library/Developer/CoreSimulator/Devices/24942F86-3060-4B2A-962B-60BD9614F1F4/data/Containers/Bundle/Application/13FF1FEF-7257-49BB-8287-1FCD0810CD0E/Runner.app/Frameworks/PostHog.framework/PostHog
       0x104ce4000 -        0x104ceffff org.cocoapods.Reachability (5.2.4) <d5570c0a-e256-3e20-8732-c2b425cc2bce> /Users/<USER>/Library/Developer/CoreSimulator/Devices/24942F86-3060-4B2A-962B-60BD9614F1F4/data/Containers/Bundle/Application/13FF1FEF-7257-49BB-8287-1FCD0810CD0E/Runner.app/Frameworks/Reachability.framework/Reachability
       0x10503c000 -        0x1050bbfff org.cocoapods.SDWebImage (5.21.1) <43368b18-84f3-3020-bd85-9da338c90e8a> /Users/<USER>/Library/Developer/CoreSimulator/Devices/24942F86-3060-4B2A-962B-60BD9614F1F4/data/Containers/Bundle/Application/13FF1FEF-7257-49BB-8287-1FCD0810CD0E/Runner.app/Frameworks/SDWebImage.framework/SDWebImage
       0x104548000 -        0x104553fff org.cocoapods.SDWebImageWebPCoder (0.14.6) <070de3a9-68cf-36c8-965a-e06b41c095bc> /Users/<USER>/Library/Developer/CoreSimulator/Devices/24942F86-3060-4B2A-962B-60BD9614F1F4/data/Containers/Bundle/Application/13FF1FEF-7257-49BB-8287-1FCD0810CD0E/Runner.app/Frameworks/SDWebImageWebPCoder.framework/SDWebImageWebPCoder
       0x104f3c000 -        0x104f53fff org.cocoapods.SwiftyGif (5.4.5) <88cedd7c-e176-32e9-aaa4-99c7c8e78da9> /Users/<USER>/Library/Developer/CoreSimulator/Devices/24942F86-3060-4B2A-962B-60BD9614F1F4/data/Containers/Bundle/Application/13FF1FEF-7257-49BB-8287-1FCD0810CD0E/Runner.app/Frameworks/SwiftyGif.framework/SwiftyGif
       0x106334000 -        0x106c6bfff org.webrtc.WebRTC (1.0) <4c4c4404-5555-3144-a1e8-358e2ad27d7b> /Users/<USER>/Library/Developer/CoreSimulator/Devices/24942F86-3060-4B2A-962B-60BD9614F1F4/data/Containers/Bundle/Application/13FF1FEF-7257-49BB-8287-1FCD0810CD0E/Runner.app/Frameworks/WebRTC.framework/WebRTC
       0x10497c000 -        0x10499bfff org.cocoapods.audioplayers-darwin (0.0.1) <5dba94ce-5e49-3df4-a5dc-1b20006850a3> /Users/<USER>/Library/Developer/CoreSimulator/Devices/24942F86-3060-4B2A-962B-60BD9614F1F4/data/Containers/Bundle/Application/13FF1FEF-7257-49BB-8287-1FCD0810CD0E/Runner.app/Frameworks/audioplayers_darwin.framework/audioplayers_darwin
       0x104f80000 -        0x104f8bfff org.cocoapods.background-fetch (1.3.8) <3082c5bb-a16c-31e6-8582-dcdb7654da58> /Users/<USER>/Library/Developer/CoreSimulator/Devices/24942F86-3060-4B2A-962B-60BD9614F1F4/data/Containers/Bundle/Application/13FF1FEF-7257-49BB-8287-1FCD0810CD0E/Runner.app/Frameworks/background_fetch.framework/background_fetch
       0x1051f0000 -        0x10523ffff org.cocoapods.camera-avfoundation (0.0.1) <57779bd3-e5f9-39e2-8a94-6cc8e13eb4db> /Users/<USER>/Library/Developer/CoreSimulator/Devices/24942F86-3060-4B2A-962B-60BD9614F1F4/data/Containers/Bundle/Application/13FF1FEF-7257-49BB-8287-1FCD0810CD0E/Runner.app/Frameworks/camera_avfoundation.framework/camera_avfoundation
       0x104fa4000 -        0x104faffff org.cocoapods.connectivity-plus (0.0.1) <a9267679-a863-3c9c-86b8-421fa69f8fdd> /Users/<USER>/Library/Developer/CoreSimulator/Devices/24942F86-3060-4B2A-962B-60BD9614F1F4/data/Containers/Bundle/Application/13FF1FEF-7257-49BB-8287-1FCD0810CD0E/Runner.app/Frameworks/connectivity_plus.framework/connectivity_plus
       0x104fd0000 -        0x104fdffff org.cocoapods.cupertino-http (0.0.1) <c534a179-fcdb-3f80-b7d3-00ce32507584> /Users/<USER>/Library/Developer/CoreSimulator/Devices/24942F86-3060-4B2A-962B-60BD9614F1F4/data/Containers/Bundle/Application/13FF1FEF-7257-49BB-8287-1FCD0810CD0E/Runner.app/Frameworks/cupertino_http.framework/cupertino_http
       0x104ff8000 -        0x10500bfff org.cocoapods.file-picker (0.0.1) <c0502278-71ed-3e27-ade8-7c16e317672e> /Users/<USER>/Library/Developer/CoreSimulator/Devices/24942F86-3060-4B2A-962B-60BD9614F1F4/data/Containers/Bundle/Application/13FF1FEF-7257-49BB-8287-1FCD0810CD0E/Runner.app/Frameworks/file_picker.framework/file_picker
       0x1052a4000 -        0x1052c7fff org.cocoapods.flutter-image-compress-common (1.0.0) <e0b45e4d-d6f6-3269-ba72-505c1df8f371> /Users/<USER>/Library/Developer/CoreSimulator/Devices/24942F86-3060-4B2A-962B-60BD9614F1F4/data/Containers/Bundle/Application/13FF1FEF-7257-49BB-8287-1FCD0810CD0E/Runner.app/Frameworks/flutter_image_compress_common.framework/flutter_image_compress_common
       0x105138000 -        0x105147fff org.cocoapods.flutter-local-notifications (0.0.1) <a3de282d-4d89-34ed-b74c-6a0c45a157aa> /Users/<USER>/Library/Developer/CoreSimulator/Devices/24942F86-3060-4B2A-962B-60BD9614F1F4/data/Containers/Bundle/Application/13FF1FEF-7257-49BB-8287-1FCD0810CD0E/Runner.app/Frameworks/flutter_local_notifications.framework/flutter_local_notifications
       0x105160000 -        0x10516ffff org.cocoapods.flutter-secure-storage (6.0.0) <d709d26f-6cb7-3452-91d8-5ab0d96da42e> /Users/<USER>/Library/Developer/CoreSimulator/Devices/24942F86-3060-4B2A-962B-60BD9614F1F4/data/Containers/Bundle/Application/13FF1FEF-7257-49BB-8287-1FCD0810CD0E/Runner.app/Frameworks/flutter_secure_storage.framework/flutter_secure_storage
       0x104e20000 -        0x104e33fff org.cocoapods.image-picker-ios (0.0.1) <d8c14994-b67e-3089-ad07-ef19b43fdc0b> /Users/<USER>/Library/Developer/CoreSimulator/Devices/24942F86-3060-4B2A-962B-60BD9614F1F4/data/Containers/Bundle/Application/13FF1FEF-7257-49BB-8287-1FCD0810CD0E/Runner.app/Frameworks/image_picker_ios.framework/image_picker_ios
       0x105924000 -        0x1059c3fff org.cocoapods.libwebp (1.5.0) <ac0f2aff-6f25-3923-951f-5204e2d38d98> /Users/<USER>/Library/Developer/CoreSimulator/Devices/24942F86-3060-4B2A-962B-60BD9614F1F4/data/Containers/Bundle/Application/13FF1FEF-7257-49BB-8287-1FCD0810CD0E/Runner.app/Frameworks/libwebp.framework/libwebp
       0x105024000 -        0x10502bfff org.cocoapods.nanopb (3.30910.0) <6762a519-8c26-374b-9732-572f22a294a8> /Users/<USER>/Library/Developer/CoreSimulator/Devices/24942F86-3060-4B2A-962B-60BD9614F1F4/data/Containers/Bundle/Application/13FF1FEF-7257-49BB-8287-1FCD0810CD0E/Runner.app/Frameworks/nanopb.framework/nanopb
       0x1051b4000 -        0x1051bbfff org.cocoapods.objective-c (0.0.1) <0d8a2c56-fdbb-3c89-803a-4f866892c0b2> /Users/<USER>/Library/Developer/CoreSimulator/Devices/24942F86-3060-4B2A-962B-60BD9614F1F4/data/Containers/Bundle/Application/13FF1FEF-7257-49BB-8287-1FCD0810CD0E/Runner.app/Frameworks/objective_c.framework/objective_c
       0x105334000 -        0x10533ffff org.cocoapods.path-provider-foundation (0.0.1) <ea818c3f-c528-3db6-9e33-cc9232a2234a> /Users/<USER>/Library/Developer/CoreSimulator/Devices/24942F86-3060-4B2A-962B-60BD9614F1F4/data/Containers/Bundle/Application/13FF1FEF-7257-49BB-8287-1FCD0810CD0E/Runner.app/Frameworks/path_provider_foundation.framework/path_provider_foundation
       0x105308000 -        0x105317fff org.cocoapods.posthog-flutter (0.0.1) <05eb2d96-d712-30ef-abfb-0c9e0bd2e106> /Users/<USER>/Library/Developer/CoreSimulator/Devices/24942F86-3060-4B2A-962B-60BD9614F1F4/data/Containers/Bundle/Application/13FF1FEF-7257-49BB-8287-1FCD0810CD0E/Runner.app/Frameworks/posthog_flutter.framework/posthog_flutter
       0x1053bc000 -        0x1053dffff org.cocoapods.record-ios (1.0.0) <4fb8fc2b-1094-37ca-aa9a-c48d9937c6de> /Users/<USER>/Library/Developer/CoreSimulator/Devices/24942F86-3060-4B2A-962B-60BD9614F1F4/data/Containers/Bundle/Application/13FF1FEF-7257-49BB-8287-1FCD0810CD0E/Runner.app/Frameworks/record_ios.framework/record_ios
       0x105a10000 -        0x105a23fff org.cocoapods.shared-preferences-foundation (0.0.1) <7f787c28-8ab8-38d1-9f4a-9c0acc03d294> /Users/<USER>/Library/Developer/CoreSimulator/Devices/24942F86-3060-4B2A-962B-60BD9614F1F4/data/Containers/Bundle/Application/13FF1FEF-7257-49BB-8287-1FCD0810CD0E/Runner.app/Frameworks/shared_preferences_foundation.framework/shared_preferences_foundation
       0x105a48000 -        0x105a5bfff org.cocoapods.sign-in-with-apple (0.0.1) <6a97bc70-a8c9-3481-bc7f-0c94463d6fc7> /Users/<USER>/Library/Developer/CoreSimulator/Devices/24942F86-3060-4B2A-962B-60BD9614F1F4/data/Containers/Bundle/Application/13FF1FEF-7257-49BB-8287-1FCD0810CD0E/Runner.app/Frameworks/sign_in_with_apple.framework/sign_in_with_apple
       0x105a80000 -        0x105a9ffff org.cocoapods.sqflite-darwin (0.0.4) <8089bff1-2204-3398-b695-4d26b27ed341> /Users/<USER>/Library/Developer/CoreSimulator/Devices/24942F86-3060-4B2A-962B-60BD9614F1F4/data/Containers/Bundle/Application/13FF1FEF-7257-49BB-8287-1FCD0810CD0E/Runner.app/Frameworks/sqflite_darwin.framework/sqflite_darwin
       0x105ac8000 -        0x105adffff org.cocoapods.video-player-avfoundation (0.0.1) <c69d2e76-b993-3733-b43c-444bb697ef2b> /Users/<USER>/Library/Developer/CoreSimulator/Devices/24942F86-3060-4B2A-962B-60BD9614F1F4/data/Containers/Bundle/Application/13FF1FEF-7257-49BB-8287-1FCD0810CD0E/Runner.app/Frameworks/video_player_avfoundation.framework/video_player_avfoundation
       0x105cb0000 -        0x105d5bfff org.cocoapods.webview-flutter-wkwebview (0.0.1) <ec39c002-5a1d-3a12-a28b-6f9ab7cfcbb4> /Users/<USER>/Library/Developer/CoreSimulator/Devices/24942F86-3060-4B2A-962B-60BD9614F1F4/data/Containers/Bundle/Application/13FF1FEF-7257-49BB-8287-1FCD0810CD0E/Runner.app/Frameworks/webview_flutter_wkwebview.framework/webview_flutter_wkwebview
       0x105b74000 -        0x105b9bfff org.cocoapods.workmanager (0.0.1) <f1b859ba-2861-3133-a066-20a0754936f2> /Users/<USER>/Library/Developer/CoreSimulator/Devices/24942F86-3060-4B2A-962B-60BD9614F1F4/data/Containers/Bundle/Application/13FF1FEF-7257-49BB-8287-1FCD0810CD0E/Runner.app/Frameworks/workmanager.framework/workmanager
       0x109bbc000 -        0x10bd37fff io.flutter.flutter (1.0) <4c4c4445-5555-3144-a11f-921ceb984ad8> /Users/<USER>/Library/Developer/CoreSimulator/Devices/24942F86-3060-4B2A-962B-60BD9614F1F4/data/Containers/Bundle/Application/13FF1FEF-7257-49BB-8287-1FCD0810CD0E/Runner.app/Frameworks/Flutter.framework/Flutter
       0x105190000 -        0x105197fff libsystem_platform.dylib (*) <f77d8041-529c-3659-ac12-4821c1c301fc> /usr/lib/system/libsystem_platform.dylib
       0x105b00000 -        0x105b3bfff libsystem_kernel.dylib (*) <f30ad29e-a6a0-3f43-8f6e-3770f040794d> /usr/lib/system/libsystem_kernel.dylib
       0x10535c000 -        0x10536bfff libsystem_pthread.dylib (*) <7d5c0390-4e96-3458-ad9c-5c41b02beefd> /usr/lib/system/libsystem_pthread.dylib
       0x105ee0000 -        0x105eebfff libobjc-trampolines.dylib (*) <56878cbd-4b61-3d67-a830-23a1b2beaf59> /Volumes/VOLUME/*/libobjc-trampolines.dylib
       0x1800fe000 -        0x18017a79b libsystem_c.dylib (*) <d8eab09a-74cb-346d-b14e-3187db1c40db> /Volumes/VOLUME/*/libsystem_c.dylib
       0x18029b000 -        0x1802b6fff libc++abi.dylib (*) <649076f2-9c2b-3e8e-833b-d246ac43869e> /Volumes/VOLUME/*/libc++abi.dylib
       0x180068000 -        0x1800a491f libobjc.A.dylib (*) <b10e226d-4fab-3450-9a4c-071d3d0edf3a> /Volumes/VOLUME/*/libobjc.A.dylib
       0x180396000 -        0x1807adfff com.apple.CoreFoundation (6.9) <ae27f481-c1fa-359c-b04c-af9cda7655ff> /Volumes/VOLUME/*/CoreFoundation.framework/CoreFoundation
       0x18017b000 -        0x1801bfb9f libdispatch.dylib (*) <990151a6-fd18-3496-84e3-f565307fbc2f> /Volumes/VOLUME/*/libdispatch.dylib
       0x191002000 -        0x19100a1ff com.apple.GraphicsServices (1.0) <80b30bb2-e6e1-317e-b798-ea590de713a8> /Volumes/VOLUME/*/GraphicsServices.framework/GraphicsServices
       0x184dfb000 -        0x186b672bf com.apple.UIKitCore (1.0) <f5406608-aa34-30ba-8494-0a8b531792f5> /Volumes/VOLUME/*/UIKitCore.framework/UIKitCore
               0x0 - 0xffffffffffffffff ??? (*) <00000000-0000-0000-0000-000000000000> ???
       0x18082d000 -        0x1813dce3f com.apple.Foundation (6.9) <48eb0271-c8d1-359a-bd56-bcf3e7e37dc5> /Volumes/VOLUME/*/Foundation.framework/Foundation
       0x1800bf000 -        0x1800faa9f libxpc.dylib (*) <021c0bf8-edd4-395a-b281-2cc7a26f742f> /Volumes/VOLUME/*/libxpc.dylib
       0x22ccc6000 -        0x22ccf99bf com.apple.MTLSimDriver (368.12) <8087c5f9-8281-36e4-90a5-b6b0d0f39264> /Volumes/VOLUME/*/MTLSimDriver.framework/MTLSimDriver
       0x19fae7000 -        0x19fcf063f com.apple.Metal (368.12) <79a48c4f-daa0-307d-a147-6125da3a34a4> /Volumes/VOLUME/*/Metal.framework/Metal
       0x1b6b42000 -        0x1b6b675df com.apple.audio.caulk (1.0) <7f22c3c4-1a93-34dd-bf46-522f8d2a0e77> /Volumes/VOLUME/*/caulk.framework/caulk

External Modification Summary:
  Calls made by other processes targeting this process:
    task_for_pid: 0
    thread_create: 0
    thread_set_state: 0
  Calls made by this process:
    task_for_pid: 0
    thread_create: 0
    thread_set_state: 0
  Calls made by all processes on this machine:
    task_for_pid: 0
    thread_create: 0
    thread_set_state: 0

VM Region Summary:
ReadOnly portion of Libraries: Total=1.6G resident=0K(0%) swapped_out_or_unallocated=1.6G(100%)
Writable regions: Total=1.8G written=2072K(0%) resident=2072K(0%) swapped_out=0K(0%) unallocated=1.8G(100%)

                                VIRTUAL   REGION 
REGION TYPE                        SIZE    COUNT (non-coalesced) 
===========                     =======  ======= 
Activity Tracing                   256K        1 
ColorSync                           64K        4 
Foundation                          16K        1 
IOSurface                           16K        1 
Kernel Alloc Once                   32K        1 
MALLOC                             1.6G       51 
MALLOC guard page                  192K       12 
STACK GUARD                       56.6M       38 
Stack                             36.1M       38 
VM_ALLOCATE                      136.4M      328 
__DATA                            33.3M      821 
__DATA_CONST                      94.8M      839 
__DATA_DIRTY                       139K       11 
__FONT_DATA                        2352        1 
__LINKEDIT                       709.8M       52 
__OBJC_RO                         61.3M        1 
__OBJC_RW                         2727K        1 
__TEXT                           934.4M      854 
__TPRO_CONST                       148K        2 
dyld private memory                2.5G       14 
mapped file                      138.1M       22 
page table in kernel              2072K        1 
shared memory                       48K        2 
===========                     =======  ======= 
TOTAL                              6.2G     3096 


-----------
Full Report
-----------

{"app_name":"Runner","timestamp":"2025-07-19 22:20:45.00 -0400","app_version":"1.0.0","slice_uuid":"caf7731a-2c8a-3262-ac04-f72f06f73eab","build_version":"1","platform":7,"bundleID":"com.hopenapp.hopen","share_with_app_devs":0,"is_first_party":0,"bug_type":"309","os_version":"macOS 26.0 (25A5306g)","roots_installed":0,"name":"Runner","incident_id":"87254F34-ED9B-4A2C-AA3A-72C851E54FF9"}
{
  "uptime" : 140000,
  "procRole" : "Foreground",
  "version" : 2,
  "userID" : 501,
  "deployVersion" : 210,
  "modelCode" : "MacBookPro18,3",
  "coalitionID" : 103843,
  "osVersion" : {
    "train" : "macOS 26.0",
    "build" : "25A5306g",
    "releaseType" : "User"
  },
  "captureTime" : "2025-07-19 22:20:39.2369 -0400",
  "codeSigningMonitor" : 1,
  "incident" : "87254F34-ED9B-4A2C-AA3A-72C851E54FF9",
  "pid" : 80306,
  "translated" : false,
  "cpuType" : "ARM-64",
  "roots_installed" : 0,
  "bug_type" : "309",
  "procLaunch" : "2025-07-19 22:20:35.3977 -0400",
  "procStartAbsTime" : 3448593715749,
  "procExitAbsTime" : 3448685403246,
  "procName" : "Runner",
  "procPath" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/24942F86-3060-4B2A-962B-60BD9614F1F4\/data\/Containers\/Bundle\/Application\/13FF1FEF-7257-49BB-8287-1FCD0810CD0E\/Runner.app\/Runner",
  "bundleInfo" : {"CFBundleShortVersionString":"1.0.0","CFBundleVersion":"1","CFBundleIdentifier":"com.hopenapp.hopen"},
  "storeInfo" : {"deviceIdentifierForVendor":"6DD9F130-DC4B-5473-B2C5-D45547FA8B3F","thirdParty":true},
  "parentProc" : "launchd_sim",
  "parentPid" : 68304,
  "coalitionName" : "com.apple.CoreSimulator.SimDevice.24942F86-3060-4B2A-962B-60BD9614F1F4",
  "crashReporterKey" : "65553204-4A28-33BB-B7F3-8A48D7F2DE89",
  "developerMode" : 1,
  "responsiblePid" : 12609,
  "responsibleProc" : "SimulatorTrampoline",
  "codeSigningID" : "com.hopenapp.hopen",
  "codeSigningTeamID" : "",
  "codeSigningFlags" : 570425857,
  "codeSigningValidationCategory" : 10,
  "codeSigningTrustLevel" : 4294967295,
  "codeSigningAuxiliaryInfo" : 0,
  "instructionByteStream" : {"beforePC":"4wAAVP17v6n9AwCRKeP\/l78DAJH9e8GowANf1sADX9YQKYDSARAA1A==","atPC":"4wAAVP17v6n9AwCRH+P\/l78DAJH9e8GowANf1sADX9ZwCoDSARAA1A=="},
  "bootSessionUUID" : "F1193B8E-9C88-43F9-978B-736CB73CED37",
  "wakeTime" : 42472,
  "sleepWakeUUID" : "3E9C9FEE-CE70-435F-BB7B-7D695E48AC2B",
  "sip" : "enabled",
  "exception" : {"codes":"0x0000000000000000, 0x0000000000000000","rawCodes":[0,0],"type":"EXC_CRASH","signal":"SIGABRT"},
  "termination" : {"flags":0,"code":6,"namespace":"SIGNAL","indicator":"Abort trap: 6","byProc":"Runner","byPid":80306},
  "extMods" : {"caller":{"thread_create":0,"thread_set_state":0,"task_for_pid":0},"system":{"thread_create":0,"thread_set_state":0,"task_for_pid":0},"targeted":{"thread_create":0,"thread_set_state":0,"task_for_pid":0},"warnings":0},
  "lastExceptionBacktrace" : [{"imageOffset":1259464,"symbol":"__exceptionPreprocess","symbolLocation":160,"imageIndex":54},{"imageOffset":178124,"symbol":"objc_exception_throw","symbolLocation":72,"imageIndex":53},{"imageOffset":1259236,"symbol":"-[NSException initWithCoder:]","symbolLocation":0,"imageIndex":54},{"imageOffset":4908,"sourceLine":115,"sourceFile":"FIRApp.m","symbol":"+[FIRApp configure]","imageIndex":8,"symbolLocation":120},{"imageOffset":19456,"sourceLine":49,"sourceFile":"AppDelegate.swift","symbol":"closure #2 in AppDelegate.application(_:didFinishLaunchingWithOptions:)","imageIndex":2,"symbolLocation":148},{"imageOffset":20252,"sourceFile":"\/<compiler-generated>","symbol":"thunk for @escaping @callee_guaranteed () -> ()","symbolLocation":48,"imageIndex":2},{"imageOffset":6024,"symbol":"_dispatch_call_block_and_release","symbolLocation":24,"imageIndex":55},{"imageOffset":115320,"symbol":"_dispatch_client_callout","symbolLocation":12,"imageIndex":55},{"imageOffset":229388,"symbol":"<deduplicated_symbol>","symbolLocation":24,"imageIndex":55},{"imageOffset":70084,"symbol":"_dispatch_main_queue_drain","symbolLocation":1184,"imageIndex":55},{"imageOffset":68884,"symbol":"_dispatch_main_queue_callback_4CF","symbolLocation":40,"imageIndex":55},{"imageOffset":601756,"symbol":"__CFRUNLOOP_IS_SERVICING_THE_MAIN_DISPATCH_QUEUE__","symbolLocation":12,"imageIndex":54},{"imageOffset":579752,"symbol":"__CFRunLoopRun","symbolLocation":1920,"imageIndex":54},{"imageOffset":576748,"symbol":"CFRunLoopRunSpecific","symbolLocation":536,"imageIndex":54},{"imageOffset":11520,"symbol":"GSEventRunModal","symbolLocation":164,"imageIndex":56},{"imageOffset":15067092,"symbol":"-[UIApplication _run]","symbolLocation":796,"imageIndex":57},{"imageOffset":15084448,"symbol":"UIApplicationMain","symbolLocation":124,"imageIndex":57},{"imageOffset":2264860,"imageIndex":57},{"imageOffset":49128,"sourceFile":"\/<compiler-generated>","symbol":"static UIApplicationDelegate.main()","symbolLocation":128,"imageIndex":2},{"imageOffset":48984,"sourceFile":"\/<compiler-generated>","symbol":"static AppDelegate.$main()","symbolLocation":44,"imageIndex":2},{"imageOffset":51388,"sourceFile":"\/<compiler-generated>","symbol":"__debug_main_executable_dylib_entry_point","symbolLocation":28,"imageIndex":2},{"imageOffset":4369028052,"imageIndex":58},{"imageOffset":35108,"symbol":"start","symbolLocation":6400,"imageIndex":0}],
  "faultingThread" : 0,
  "threads" : [{"triggered":true,"id":5931113,"threadState":{"x":[{"value":0},{"value":0},{"value":0},{"value":0},{"value":6445285019},{"value":6100679312},{"value":110},{"value":0},{"value":4368473664,"symbolLocation":0,"symbol":"_main_thread"},{"value":3887959503583834922},{"value":81},{"value":11},{"value":11},{"value":6449695478},{"value":2043},{"value":3347079230},{"value":328},{"value":3349174340},{"value":0},{"value":6},{"value":259},{"value":4368473888,"symbolLocation":224,"symbol":"_main_thread"},{"value":8368328704,"symbolLocation":1712,"symbol":"objc_debug_taggedpointer_ext_classes"},{"value":8368319168,"symbolLocation":0,"symbol":"_dispatch_main_q"},{"value":8368319168,"symbolLocation":0,"symbol":"_dispatch_main_q"},{"value":105553140712576},{"value":105553162600960},{"value":0},{"value":276}],"flavor":"ARM_THREAD_STATE64","lr":{"value":4382401260},"cpsr":{"value":1073745920},"fp":{"value":6100679168},"sp":{"value":6100679136},"esr":{"value":1442840704,"description":"(PC alignment)"},"pc":{"value":4390422620,"matchesCrashFrame":1},"far":{"value":0}},"frames":[{"imageOffset":34908,"symbol":"__pthread_kill","symbolLocation":8,"imageIndex":48},{"imageOffset":25324,"symbol":"pthread_kill","symbolLocation":264,"imageIndex":49},{"imageOffset":474792,"symbol":"abort","symbolLocation":100,"imageIndex":51},{"imageOffset":86340,"symbol":"abort_message","symbolLocation":128,"imageIndex":52},{"imageOffset":20044,"symbol":"demangling_terminate_handler()","symbolLocation":296,"imageIndex":52},{"imageOffset":29216,"symbol":"_objc_terminate()","symbolLocation":124,"imageIndex":53},{"imageOffset":83312,"symbol":"std::__terminate(void (*)())","symbolLocation":12,"imageIndex":52},{"imageOffset":95384,"symbol":"__cxxabiv1::failed_throw(__cxxabiv1::__cxa_exception*)","symbolLocation":32,"imageIndex":52},{"imageOffset":95352,"symbol":"__cxa_throw","symbolLocation":88,"imageIndex":52},{"imageOffset":178436,"symbol":"objc_exception_throw","symbolLocation":384,"imageIndex":53},{"imageOffset":1259236,"symbol":"+[NSException raise:format:]","symbolLocation":124,"imageIndex":54},{"imageOffset":4908,"sourceLine":110,"sourceFile":"FIRApp.m","symbol":"+[FIRApp configure]","imageIndex":8,"symbolLocation":120},{"imageOffset":19456,"sourceLine":49,"sourceFile":"AppDelegate.swift","symbol":"closure #2 in AppDelegate.application(_:didFinishLaunchingWithOptions:)","imageIndex":2,"symbolLocation":148},{"imageOffset":20252,"sourceFile":"\/<compiler-generated>","symbol":"thunk for @escaping @callee_guaranteed () -> ()","symbolLocation":48,"imageIndex":2},{"imageOffset":6024,"symbol":"_dispatch_call_block_and_release","symbolLocation":24,"imageIndex":55},{"imageOffset":115320,"symbol":"_dispatch_client_callout","symbolLocation":12,"imageIndex":55},{"imageOffset":229388,"symbol":"<deduplicated_symbol>","symbolLocation":24,"imageIndex":55},{"imageOffset":70084,"symbol":"_dispatch_main_queue_drain","symbolLocation":1184,"imageIndex":55},{"imageOffset":68884,"symbol":"_dispatch_main_queue_callback_4CF","symbolLocation":40,"imageIndex":55},{"imageOffset":601756,"symbol":"__CFRUNLOOP_IS_SERVICING_THE_MAIN_DISPATCH_QUEUE__","symbolLocation":12,"imageIndex":54},{"imageOffset":579752,"symbol":"__CFRunLoopRun","symbolLocation":1920,"imageIndex":54},{"imageOffset":576748,"symbol":"CFRunLoopRunSpecific","symbolLocation":536,"imageIndex":54},{"imageOffset":11520,"symbol":"GSEventRunModal","symbolLocation":164,"imageIndex":56},{"imageOffset":15067092,"symbol":"-[UIApplication _run]","symbolLocation":796,"imageIndex":57},{"imageOffset":15084448,"symbol":"UIApplicationMain","symbolLocation":124,"imageIndex":57},{"imageOffset":2264860,"imageIndex":57},{"imageOffset":49128,"sourceFile":"\/<compiler-generated>","symbol":"static UIApplicationDelegate.main()","symbolLocation":128,"imageIndex":2},{"imageOffset":48984,"sourceFile":"\/<compiler-generated>","symbol":"static AppDelegate.$main()","symbolLocation":44,"imageIndex":2},{"imageOffset":51388,"sourceFile":"\/<compiler-generated>","symbol":"__debug_main_executable_dylib_entry_point","symbolLocation":28,"imageIndex":2},{"imageOffset":4369028052,"imageIndex":58},{"imageOffset":35108,"symbol":"start","symbolLocation":6400,"imageIndex":0}]},{"id":5931177,"frames":[{"imageOffset":6532,"symbol":"start_wqthread","symbolLocation":0,"imageIndex":49}],"threadState":{"x":[{"value":6101250048},{"value":7683},{"value":6100713472},{"value":0},{"value":409604},{"value":18446744073709551615},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0}],"flavor":"ARM_THREAD_STATE64","lr":{"value":0},"cpsr":{"value":4096},"fp":{"value":0},"sp":{"value":6101250048},"esr":{"value":1442840704,"description":"(PC alignment)"},"pc":{"value":4382382468},"far":{"value":0}}},{"id":5931178,"frames":[{"imageOffset":6532,"symbol":"start_wqthread","symbolLocation":0,"imageIndex":49}],"threadState":{"x":[{"value":6101823488},{"value":7427},{"value":6101286912},{"value":0},{"value":409604},{"value":18446744073709551615},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0}],"flavor":"ARM_THREAD_STATE64","lr":{"value":0},"cpsr":{"value":4096},"fp":{"value":0},"sp":{"value":6101823488},"esr":{"value":1442840704,"description":"(PC alignment)"},"pc":{"value":4382382468},"far":{"value":0}}},{"id":5931179,"frames":[{"imageOffset":6532,"symbol":"start_wqthread","symbolLocation":0,"imageIndex":49}],"threadState":{"x":[{"value":6102396928},{"value":4611},{"value":6101860352},{"value":0},{"value":409604},{"value":18446744073709551615},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0}],"flavor":"ARM_THREAD_STATE64","lr":{"value":0},"cpsr":{"value":4096},"fp":{"value":0},"sp":{"value":6102396928},"esr":{"value":1442840704,"description":"(PC alignment)"},"pc":{"value":4382382468},"far":{"value":0}}},{"id":5931182,"frames":[{"imageOffset":6532,"symbol":"start_wqthread","symbolLocation":0,"imageIndex":49}],"threadState":{"x":[{"value":6102970368},{"value":9219},{"value":6102433792},{"value":0},{"value":409604},{"value":18446744073709551615},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0}],"flavor":"ARM_THREAD_STATE64","lr":{"value":0},"cpsr":{"value":4096},"fp":{"value":0},"sp":{"value":6102970368},"esr":{"value":1442840704,"description":"(PC alignment)"},"pc":{"value":4382382468},"far":{"value":0}}},{"id":5931183,"name":"com.apple.uikit.eventfetch-thread","threadState":{"x":[{"value":268451845},{"value":21592279046},{"value":8589934592},{"value":67083094196224},{"value":4294967295},{"value":67083094196224},{"value":2},{"value":4294967295},{"value":0},{"value":17179869184},{"value":0},{"value":2},{"value":0},{"value":0},{"value":15619},{"value":3072},{"value":18446744073709551569},{"value":88},{"value":0},{"value":4294967295},{"value":2},{"value":67083094196224},{"value":4294967295},{"value":67083094196224},{"value":6103539080},{"value":8589934592},{"value":21592279046},{"value":18446744073709550527},{"value":4412409862}],"flavor":"ARM_THREAD_STATE64","lr":{"value":4390459660},"cpsr":{"value":4096},"fp":{"value":6103538928},"sp":{"value":6103538848},"esr":{"value":1442840704,"description":"(PC alignment)"},"pc":{"value":4390390640},"far":{"value":0}},"frames":[{"imageOffset":2928,"symbol":"mach_msg2_trap","symbolLocation":8,"imageIndex":48},{"imageOffset":71948,"symbol":"mach_msg2_internal","symbolLocation":72,"imageIndex":48},{"imageOffset":35856,"symbol":"mach_msg_overwrite","symbolLocation":480,"imageIndex":48},{"imageOffset":3812,"symbol":"mach_msg","symbolLocation":20,"imageIndex":48},{"imageOffset":601028,"symbol":"__CFRunLoopServiceMachPort","symbolLocation":156,"imageIndex":54},{"imageOffset":578980,"symbol":"__CFRunLoopRun","symbolLocation":1148,"imageIndex":54},{"imageOffset":576748,"symbol":"CFRunLoopRunSpecific","symbolLocation":536,"imageIndex":54},{"imageOffset":7213964,"symbol":"-[NSRunLoop(NSRunLoop) runMode:beforeDate:]","symbolLocation":208,"imageIndex":59},{"imageOffset":7214508,"symbol":"-[NSRunLoop(NSRunLoop) runUntilDate:]","symbolLocation":60,"imageIndex":59},{"imageOffset":15778704,"symbol":"-[UIEventFetcher threadMain]","symbolLocation":408,"imageIndex":57},{"imageOffset":7373128,"symbol":"__NSThread__start__","symbolLocation":716,"imageIndex":59},{"imageOffset":26096,"symbol":"_pthread_start","symbolLocation":104,"imageIndex":49},{"imageOffset":6552,"symbol":"thread_start","symbolLocation":8,"imageIndex":49}]},{"id":5931184,"frames":[{"imageOffset":6532,"symbol":"start_wqthread","symbolLocation":0,"imageIndex":49}],"threadState":{"x":[{"value":6104117248},{"value":14083},{"value":6103580672},{"value":0},{"value":409604},{"value":18446744073709551615},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0}],"flavor":"ARM_THREAD_STATE64","lr":{"value":0},"cpsr":{"value":4096},"fp":{"value":0},"sp":{"value":6104117248},"esr":{"value":1442840704,"description":"(PC alignment)"},"pc":{"value":4382382468},"far":{"value":0}}},{"id":5931185,"frames":[{"imageOffset":6532,"symbol":"start_wqthread","symbolLocation":0,"imageIndex":49}],"threadState":{"x":[{"value":6104690688},{"value":21251},{"value":6104154112},{"value":0},{"value":409604},{"value":18446744073709551615},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0}],"flavor":"ARM_THREAD_STATE64","lr":{"value":0},"cpsr":{"value":4096},"fp":{"value":0},"sp":{"value":6104690688},"esr":{"value":1442840704,"description":"(PC alignment)"},"pc":{"value":4382382468},"far":{"value":0}}},{"id":5931187,"frames":[{"imageOffset":6532,"symbol":"start_wqthread","symbolLocation":0,"imageIndex":49}],"threadState":{"x":[{"value":6105264128},{"value":17923},{"value":6104727552},{"value":0},{"value":409604},{"value":18446744073709551615},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0}],"flavor":"ARM_THREAD_STATE64","lr":{"value":0},"cpsr":{"value":4096},"fp":{"value":0},"sp":{"value":6105264128},"esr":{"value":1442840704,"description":"(PC alignment)"},"pc":{"value":4382382468},"far":{"value":0}}},{"id":5931188,"frames":[{"imageOffset":2928,"symbol":"mach_msg2_trap","symbolLocation":8,"imageIndex":48},{"imageOffset":71948,"symbol":"mach_msg2_internal","symbolLocation":72,"imageIndex":48},{"imageOffset":35856,"symbol":"mach_msg_overwrite","symbolLocation":480,"imageIndex":48},{"imageOffset":3812,"symbol":"mach_msg","symbolLocation":20,"imageIndex":48},{"imageOffset":121264,"symbol":"_dispatch_mach_send_and_wait_for_reply","symbolLocation":564,"imageIndex":55},{"imageOffset":122188,"symbol":"dispatch_mach_send_with_result_and_wait_for_reply","symbolLocation":56,"imageIndex":55},{"imageOffset":58448,"symbol":"xpc_connection_send_message_with_reply_sync","symbolLocation":252,"imageIndex":60},{"imageOffset":9568,"symbol":"sendXPCMessageWithReplySync(NSObject<OS_xpc_object>*, NSObject<OS_xpc_object>*)","symbolLocation":48,"imageIndex":61},{"imageOffset":6776,"symbol":"dispatchOperationWihReply(NSObject<OS_xpc_object>*, NSObject<OS_dispatch_data>*, unsigned long long, void (NSObject<OS_xpc_object>*) block_pointer, void (void const*, unsigned long) block_pointer)","symbolLocation":156,"imageIndex":61},{"imageOffset":21448,"symbol":"-[MTLSimDevice newRenderPipelineWithDescriptor:vertexVariant:fragmentVariant:errorMessage:]","symbolLocation":328,"imageIndex":61},{"imageOffset":1040412,"symbol":"newRenderPipeline(MTLProgramObject*, MTLFunctionVariant*, MTLProgramObject*, MTLFunctionVariant*, MTLProgramObject*, MTLFunctionVariant*, NSObject<OS_dispatch_data>*, MTLProgramObject*, MTLFunctionVariant*, NSObject<OS_dispatch_data>*, _MTLDevice*, NSObject<OS_dispatch_data>*, MTLRenderPipelineDescriptor*, unsigned long, unsigned long, MTLRenderPipelineReflection**, NSMutableDictionary*, NSObject<OS_dispatch_data>*, NSObject<OS_dispatch_data>*, void (id<MTLRenderPipelineState>, MTLRenderPipelineReflection*, NSError*) block_pointer, NSError*)","symbolLocation":140,"imageIndex":62},{"imageOffset":1055636,"symbol":"__266-[MTLCompiler createVertexStageAndLinkPipelineWithFragment:fragmentVariant:vertexFunction:serializedVertexDescriptor:descriptor:airDescriptor:destinationArchive:options:reflection:compileStatistics:fragmentCompileTimeData:pipelineArchiverId:error:completionHandler:]_block_invoke_5","symbolLocation":264,"imageIndex":62},{"imageOffset":6024,"symbol":"_dispatch_call_block_and_release","symbolLocation":24,"imageIndex":55},{"imageOffset":115320,"symbol":"_dispatch_client_callout","symbolLocation":12,"imageIndex":55},{"imageOffset":43728,"symbol":"_dispatch_lane_serial_drain","symbolLocation":984,"imageIndex":55},{"imageOffset":46480,"symbol":"_dispatch_lane_invoke","symbolLocation":396,"imageIndex":55},{"imageOffset":91008,"symbol":"_dispatch_root_queue_drain_deferred_wlh","symbolLocation":288,"imageIndex":55},{"imageOffset":88564,"symbol":"_dispatch_workloop_worker_thread","symbolLocation":440,"imageIndex":55},{"imageOffset":11216,"symbol":"_pthread_wqthread","symbolLocation":288,"imageIndex":49},{"imageOffset":6540,"symbol":"start_wqthread","symbolLocation":8,"imageIndex":49}],"threadState":{"x":[{"value":268451845},{"value":17297326606},{"value":0},{"value":23811},{"value":0},{"value":172636210462720},{"value":16384},{"value":0},{"value":0},{"value":17179869184},{"value":16384},{"value":0},{"value":0},{"value":0},{"value":40195},{"value":16384},{"value":18446744073709551569},{"value":0},{"value":0},{"value":0},{"value":16384},{"value":172636210462720},{"value":0},{"value":23811},{"value":6105818096},{"value":0},{"value":17297326606},{"value":18446744073709550527},{"value":117457422}],"flavor":"ARM_THREAD_STATE64","lr":{"value":4390459660},"cpsr":{"value":4096},"fp":{"value":6105817760},"sp":{"value":6105817680},"esr":{"value":1442840704,"description":"(PC alignment)"},"pc":{"value":4390390640},"far":{"value":0}}},{"id":5931189,"frames":[{"imageOffset":6532,"symbol":"start_wqthread","symbolLocation":0,"imageIndex":49}],"threadState":{"x":[{"value":6106411008},{"value":18435},{"value":6105874432},{"value":0},{"value":409604},{"value":18446744073709551615},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0}],"flavor":"ARM_THREAD_STATE64","lr":{"value":0},"cpsr":{"value":4096},"fp":{"value":0},"sp":{"value":6106411008},"esr":{"value":1442840704,"description":"(PC alignment)"},"pc":{"value":4382382468},"far":{"value":0}}},{"id":5931190,"frames":[{"imageOffset":6532,"symbol":"start_wqthread","symbolLocation":0,"imageIndex":49}],"threadState":{"x":[{"value":6106984448},{"value":18691},{"value":6106447872},{"value":0},{"value":409604},{"value":18446744073709551615},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0}],"flavor":"ARM_THREAD_STATE64","lr":{"value":0},"cpsr":{"value":4096},"fp":{"value":0},"sp":{"value":6106984448},"esr":{"value":1442840704,"description":"(PC alignment)"},"pc":{"value":4382382468},"far":{"value":0}}},{"id":5931191,"frames":[{"imageOffset":6532,"symbol":"start_wqthread","symbolLocation":0,"imageIndex":49}],"threadState":{"x":[{"value":6107557888},{"value":19459},{"value":6107021312},{"value":0},{"value":409604},{"value":18446744073709551615},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0}],"flavor":"ARM_THREAD_STATE64","lr":{"value":0},"cpsr":{"value":4096},"fp":{"value":0},"sp":{"value":6107557888},"esr":{"value":1442840704,"description":"(PC alignment)"},"pc":{"value":4382382468},"far":{"value":0}}},{"id":5931192,"frames":[{"imageOffset":6532,"symbol":"start_wqthread","symbolLocation":0,"imageIndex":49}],"threadState":{"x":[{"value":6108131328},{"value":18947},{"value":6107594752},{"value":0},{"value":409604},{"value":18446744073709551615},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0}],"flavor":"ARM_THREAD_STATE64","lr":{"value":0},"cpsr":{"value":4096},"fp":{"value":0},"sp":{"value":6108131328},"esr":{"value":1442840704,"description":"(PC alignment)"},"pc":{"value":4382382468},"far":{"value":0}}},{"id":5931193,"frames":[{"imageOffset":6532,"symbol":"start_wqthread","symbolLocation":0,"imageIndex":49}],"threadState":{"x":[{"value":6108704768},{"value":19203},{"value":6108168192},{"value":0},{"value":409604},{"value":18446744073709551615},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0}],"flavor":"ARM_THREAD_STATE64","lr":{"value":0},"cpsr":{"value":4096},"fp":{"value":0},"sp":{"value":6108704768},"esr":{"value":1442840704,"description":"(PC alignment)"},"pc":{"value":4382382468},"far":{"value":0}}},{"id":5931194,"frames":[{"imageOffset":6532,"symbol":"start_wqthread","symbolLocation":0,"imageIndex":49}],"threadState":{"x":[{"value":6109278208},{"value":21763},{"value":6108741632},{"value":0},{"value":409604},{"value":18446744073709551615},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0}],"flavor":"ARM_THREAD_STATE64","lr":{"value":0},"cpsr":{"value":4096},"fp":{"value":0},"sp":{"value":6109278208},"esr":{"value":1442840704,"description":"(PC alignment)"},"pc":{"value":4382382468},"far":{"value":0}}},{"id":5931195,"frames":[{"imageOffset":6532,"symbol":"start_wqthread","symbolLocation":0,"imageIndex":49}],"threadState":{"x":[{"value":6109851648},{"value":22019},{"value":6109315072},{"value":0},{"value":409604},{"value":18446744073709551615},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0}],"flavor":"ARM_THREAD_STATE64","lr":{"value":0},"cpsr":{"value":4096},"fp":{"value":0},"sp":{"value":6109851648},"esr":{"value":1442840704,"description":"(PC alignment)"},"pc":{"value":4382382468},"far":{"value":0}}},{"id":5931196,"frames":[{"imageOffset":6532,"symbol":"start_wqthread","symbolLocation":0,"imageIndex":49}],"threadState":{"x":[{"value":6110425088},{"value":32515},{"value":6109888512},{"value":0},{"value":409604},{"value":18446744073709551615},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0}],"flavor":"ARM_THREAD_STATE64","lr":{"value":0},"cpsr":{"value":4096},"fp":{"value":0},"sp":{"value":6110425088},"esr":{"value":1442840704,"description":"(PC alignment)"},"pc":{"value":4382382468},"far":{"value":0}}},{"id":5931197,"frames":[{"imageOffset":6532,"symbol":"start_wqthread","symbolLocation":0,"imageIndex":49}],"threadState":{"x":[{"value":6110998528},{"value":32259},{"value":6110461952},{"value":6110997376},{"value":5128194},{"value":1},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0}],"flavor":"ARM_THREAD_STATE64","lr":{"value":0},"cpsr":{"value":4096},"fp":{"value":0},"sp":{"value":6110997360},"esr":{"value":1442840704,"description":"(PC alignment)"},"pc":{"value":4382382468},"far":{"value":0}}},{"id":5931198,"frames":[{"imageOffset":6532,"symbol":"start_wqthread","symbolLocation":0,"imageIndex":49}],"threadState":{"x":[{"value":6111571968},{"value":22275},{"value":6111035392},{"value":0},{"value":409602},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0}],"flavor":"ARM_THREAD_STATE64","lr":{"value":0},"cpsr":{"value":4096},"fp":{"value":0},"sp":{"value":6111571968},"esr":{"value":1442840704,"description":"(PC alignment)"},"pc":{"value":4382382468},"far":{"value":0}}},{"id":5931199,"frames":[{"imageOffset":6532,"symbol":"start_wqthread","symbolLocation":0,"imageIndex":49}],"threadState":{"x":[{"value":6112145408},{"value":22531},{"value":6111608832},{"value":0},{"value":409604},{"value":18446744073709551615},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0}],"flavor":"ARM_THREAD_STATE64","lr":{"value":0},"cpsr":{"value":4096},"fp":{"value":0},"sp":{"value":6112145408},"esr":{"value":1442840704,"description":"(PC alignment)"},"pc":{"value":4382382468},"far":{"value":0}}},{"id":5931201,"name":"io.flutter.1.raster","threadState":{"x":[{"value":268451845},{"value":21592279046},{"value":8589934592},{"value":120959163957248},{"value":0},{"value":120959163957248},{"value":2},{"value":4294967295},{"value":0},{"value":17179869184},{"value":0},{"value":2},{"value":0},{"value":0},{"value":28163},{"value":3072},{"value":18446744073709551569},{"value":1099511628034},{"value":0},{"value":4294967295},{"value":2},{"value":120959163957248},{"value":0},{"value":120959163957248},{"value":6114287624},{"value":8589934592},{"value":21592279046},{"value":18446744073709550527},{"value":4412409862}],"flavor":"ARM_THREAD_STATE64","lr":{"value":4390459660},"cpsr":{"value":4096},"fp":{"value":6114287472},"sp":{"value":6114287392},"esr":{"value":1442840704,"description":"(PC alignment)"},"pc":{"value":4390390640},"far":{"value":0}},"frames":[{"imageOffset":2928,"symbol":"mach_msg2_trap","symbolLocation":8,"imageIndex":48},{"imageOffset":71948,"symbol":"mach_msg2_internal","symbolLocation":72,"imageIndex":48},{"imageOffset":35856,"symbol":"mach_msg_overwrite","symbolLocation":480,"imageIndex":48},{"imageOffset":3812,"symbol":"mach_msg","symbolLocation":20,"imageIndex":48},{"imageOffset":601028,"symbol":"__CFRunLoopServiceMachPort","symbolLocation":156,"imageIndex":54},{"imageOffset":578980,"symbol":"__CFRunLoopRun","symbolLocation":1148,"imageIndex":54},{"imageOffset":576748,"symbol":"CFRunLoopRunSpecific","symbolLocation":536,"imageIndex":54},{"imageOffset":582712,"symbol":"fml::MessageLoopDarwin::Run()","symbolLocation":88,"imageIndex":46},{"imageOffset":555012,"symbol":"fml::MessageLoopImpl::DoRun()","symbolLocation":40,"imageIndex":46},{"imageOffset":577744,"symbol":"std::_fl::__function::__func<fml::Thread::Thread(std::_fl::function<void (fml::Thread::ThreadConfig const&)> const&, fml::Thread::ThreadConfig const&)::$_0, std::_fl::allocator<fml::Thread::Thread(std::_fl::function<void (fml::Thread::ThreadConfig const&)> const&, fml::Thread::ThreadConfig const&)::$_0>, void ()>::operator()()","symbolLocation":184,"imageIndex":46},{"imageOffset":576932,"symbol":"fml::ThreadHandle::ThreadHandle(std::_fl::function<void ()>&&)::$_0::__invoke(void*)","symbolLocation":36,"imageIndex":46},{"imageOffset":26096,"symbol":"_pthread_start","symbolLocation":104,"imageIndex":49},{"imageOffset":6552,"symbol":"thread_start","symbolLocation":8,"imageIndex":49}]},{"id":5931202,"name":"io.flutter.1.io","threadState":{"x":[{"value":268451845},{"value":21592279046},{"value":8589934592},{"value":109964047679488},{"value":0},{"value":109964047679488},{"value":2},{"value":4294967295},{"value":0},{"value":17179869184},{"value":0},{"value":2},{"value":0},{"value":0},{"value":25603},{"value":3072},{"value":18446744073709551569},{"value":2},{"value":0},{"value":4294967295},{"value":2},{"value":109964047679488},{"value":0},{"value":109964047679488},{"value":6116433928},{"value":8589934592},{"value":21592279046},{"value":18446744073709550527},{"value":4412409862}],"flavor":"ARM_THREAD_STATE64","lr":{"value":4390459660},"cpsr":{"value":4096},"fp":{"value":6116433776},"sp":{"value":6116433696},"esr":{"value":1442840704,"description":"(PC alignment)"},"pc":{"value":4390390640},"far":{"value":0}},"frames":[{"imageOffset":2928,"symbol":"mach_msg2_trap","symbolLocation":8,"imageIndex":48},{"imageOffset":71948,"symbol":"mach_msg2_internal","symbolLocation":72,"imageIndex":48},{"imageOffset":35856,"symbol":"mach_msg_overwrite","symbolLocation":480,"imageIndex":48},{"imageOffset":3812,"symbol":"mach_msg","symbolLocation":20,"imageIndex":48},{"imageOffset":601028,"symbol":"__CFRunLoopServiceMachPort","symbolLocation":156,"imageIndex":54},{"imageOffset":578980,"symbol":"__CFRunLoopRun","symbolLocation":1148,"imageIndex":54},{"imageOffset":576748,"symbol":"CFRunLoopRunSpecific","symbolLocation":536,"imageIndex":54},{"imageOffset":582712,"symbol":"fml::MessageLoopDarwin::Run()","symbolLocation":88,"imageIndex":46},{"imageOffset":555012,"symbol":"fml::MessageLoopImpl::DoRun()","symbolLocation":40,"imageIndex":46},{"imageOffset":577744,"symbol":"std::_fl::__function::__func<fml::Thread::Thread(std::_fl::function<void (fml::Thread::ThreadConfig const&)> const&, fml::Thread::ThreadConfig const&)::$_0, std::_fl::allocator<fml::Thread::Thread(std::_fl::function<void (fml::Thread::ThreadConfig const&)> const&, fml::Thread::ThreadConfig const&)::$_0>, void ()>::operator()()","symbolLocation":184,"imageIndex":46},{"imageOffset":576932,"symbol":"fml::ThreadHandle::ThreadHandle(std::_fl::function<void ()>&&)::$_0::__invoke(void*)","symbolLocation":36,"imageIndex":46},{"imageOffset":26096,"symbol":"_pthread_start","symbolLocation":104,"imageIndex":49},{"imageOffset":6552,"symbol":"thread_start","symbolLocation":8,"imageIndex":49}]},{"id":5931203,"name":"io.flutter.1.profiler","threadState":{"x":[{"value":268451845},{"value":21592279046},{"value":8589934592},{"value":184730838368256},{"value":0},{"value":184730838368256},{"value":2},{"value":4294967295},{"value":0},{"value":17179869184},{"value":0},{"value":2},{"value":0},{"value":0},{"value":43011},{"value":3072},{"value":18446744073709551569},{"value":2},{"value":0},{"value":4294967295},{"value":2},{"value":184730838368256},{"value":0},{"value":184730838368256},{"value":6118580232},{"value":8589934592},{"value":21592279046},{"value":18446744073709550527},{"value":4412409862}],"flavor":"ARM_THREAD_STATE64","lr":{"value":4390459660},"cpsr":{"value":4096},"fp":{"value":6118580080},"sp":{"value":6118580000},"esr":{"value":1442840704,"description":"(PC alignment)"},"pc":{"value":4390390640},"far":{"value":0}},"frames":[{"imageOffset":2928,"symbol":"mach_msg2_trap","symbolLocation":8,"imageIndex":48},{"imageOffset":71948,"symbol":"mach_msg2_internal","symbolLocation":72,"imageIndex":48},{"imageOffset":35856,"symbol":"mach_msg_overwrite","symbolLocation":480,"imageIndex":48},{"imageOffset":3812,"symbol":"mach_msg","symbolLocation":20,"imageIndex":48},{"imageOffset":601028,"symbol":"__CFRunLoopServiceMachPort","symbolLocation":156,"imageIndex":54},{"imageOffset":578980,"symbol":"__CFRunLoopRun","symbolLocation":1148,"imageIndex":54},{"imageOffset":576748,"symbol":"CFRunLoopRunSpecific","symbolLocation":536,"imageIndex":54},{"imageOffset":582712,"symbol":"fml::MessageLoopDarwin::Run()","symbolLocation":88,"imageIndex":46},{"imageOffset":555012,"symbol":"fml::MessageLoopImpl::DoRun()","symbolLocation":40,"imageIndex":46},{"imageOffset":577744,"symbol":"std::_fl::__function::__func<fml::Thread::Thread(std::_fl::function<void (fml::Thread::ThreadConfig const&)> const&, fml::Thread::ThreadConfig const&)::$_0, std::_fl::allocator<fml::Thread::Thread(std::_fl::function<void (fml::Thread::ThreadConfig const&)> const&, fml::Thread::ThreadConfig const&)::$_0>, void ()>::operator()()","symbolLocation":184,"imageIndex":46},{"imageOffset":576932,"symbol":"fml::ThreadHandle::ThreadHandle(std::_fl::function<void ()>&&)::$_0::__invoke(void*)","symbolLocation":36,"imageIndex":46},{"imageOffset":26096,"symbol":"_pthread_start","symbolLocation":104,"imageIndex":49},{"imageOffset":6552,"symbol":"thread_start","symbolLocation":8,"imageIndex":49}]},{"id":5931204,"name":"io.worker.1","threadState":{"x":[{"value":4},{"value":0},{"value":1024},{"value":0},{"value":0},{"value":160},{"value":0},{"value":0},{"value":6119157208},{"value":0},{"value":256},{"value":1099511628034},{"value":1099511628034},{"value":256},{"value":0},{"value":1099511628032},{"value":305},{"value":125},{"value":0},{"value":4410338344},{"value":4410338408},{"value":6119157984},{"value":0},{"value":0},{"value":1024},{"value":1025},{"value":1280},{"value":1},{"value":105553118564256}],"flavor":"ARM_THREAD_STATE64","lr":{"value":4382403256},"cpsr":{"value":1610616832},"fp":{"value":6119157328},"sp":{"value":6119157184},"esr":{"value":1442840704,"description":"(PC alignment)"},"pc":{"value":4390404128},"far":{"value":0}},"frames":[{"imageOffset":16416,"symbol":"__psynch_cvwait","symbolLocation":8,"imageIndex":48},{"imageOffset":27320,"symbol":"_pthread_cond_wait","symbolLocation":976,"imageIndex":49},{"imageOffset":411992,"symbol":"std::_fl::condition_variable::wait(std::_fl::unique_lock<std::_fl::mutex>&)","symbolLocation":24,"imageIndex":46},{"imageOffset":540212,"symbol":"fml::ConcurrentMessageLoop::WorkerMain()","symbolLocation":140,"imageIndex":46},{"imageOffset":542560,"symbol":"void* std::_fl::__thread_proxy[abi:nn210000]<std::_fl::tuple<std::_fl::unique_ptr<std::_fl::__thread_struct, std::_fl::default_delete<std::_fl::__thread_struct>>, fml::ConcurrentMessageLoop::ConcurrentMessageLoop(unsigned long)::$_0>>(void*)","symbolLocation":212,"imageIndex":46},{"imageOffset":26096,"symbol":"_pthread_start","symbolLocation":104,"imageIndex":49},{"imageOffset":6552,"symbol":"thread_start","symbolLocation":8,"imageIndex":49}]},{"id":5931205,"name":"io.worker.2","threadState":{"x":[{"value":4},{"value":0},{"value":1024},{"value":0},{"value":0},{"value":160},{"value":0},{"value":0},{"value":6119730648},{"value":0},{"value":512},{"value":2199023256066},{"value":2199023256066},{"value":512},{"value":0},{"value":2199023256064},{"value":305},{"value":126},{"value":0},{"value":4410338344},{"value":4410338408},{"value":6119731424},{"value":0},{"value":0},{"value":1024},{"value":1024},{"value":2048},{"value":1},{"value":105553118564288}],"flavor":"ARM_THREAD_STATE64","lr":{"value":4382403256},"cpsr":{"value":1610616832},"fp":{"value":6119730768},"sp":{"value":6119730624},"esr":{"value":1442840704,"description":"(PC alignment)"},"pc":{"value":4390404128},"far":{"value":0}},"frames":[{"imageOffset":16416,"symbol":"__psynch_cvwait","symbolLocation":8,"imageIndex":48},{"imageOffset":27320,"symbol":"_pthread_cond_wait","symbolLocation":976,"imageIndex":49},{"imageOffset":411992,"symbol":"std::_fl::condition_variable::wait(std::_fl::unique_lock<std::_fl::mutex>&)","symbolLocation":24,"imageIndex":46},{"imageOffset":540212,"symbol":"fml::ConcurrentMessageLoop::WorkerMain()","symbolLocation":140,"imageIndex":46},{"imageOffset":542560,"symbol":"void* std::_fl::__thread_proxy[abi:nn210000]<std::_fl::tuple<std::_fl::unique_ptr<std::_fl::__thread_struct, std::_fl::default_delete<std::_fl::__thread_struct>>, fml::ConcurrentMessageLoop::ConcurrentMessageLoop(unsigned long)::$_0>>(void*)","symbolLocation":212,"imageIndex":46},{"imageOffset":26096,"symbol":"_pthread_start","symbolLocation":104,"imageIndex":49},{"imageOffset":6552,"symbol":"thread_start","symbolLocation":8,"imageIndex":49}]},{"id":5931206,"name":"io.worker.3","threadState":{"x":[{"value":4},{"value":0},{"value":1024},{"value":0},{"value":0},{"value":160},{"value":0},{"value":0},{"value":6120304088},{"value":0},{"value":512},{"value":2199023256066},{"value":2199023256066},{"value":512},{"value":0},{"value":2199023256064},{"value":305},{"value":127},{"value":0},{"value":4410338344},{"value":4410338408},{"value":6120304864},{"value":0},{"value":0},{"value":1024},{"value":1024},{"value":1792},{"value":1},{"value":105553118564320}],"flavor":"ARM_THREAD_STATE64","lr":{"value":4382403256},"cpsr":{"value":1610616832},"fp":{"value":6120304208},"sp":{"value":6120304064},"esr":{"value":1442840704,"description":"(PC alignment)"},"pc":{"value":4390404128},"far":{"value":0}},"frames":[{"imageOffset":16416,"symbol":"__psynch_cvwait","symbolLocation":8,"imageIndex":48},{"imageOffset":27320,"symbol":"_pthread_cond_wait","symbolLocation":976,"imageIndex":49},{"imageOffset":411992,"symbol":"std::_fl::condition_variable::wait(std::_fl::unique_lock<std::_fl::mutex>&)","symbolLocation":24,"imageIndex":46},{"imageOffset":540212,"symbol":"fml::ConcurrentMessageLoop::WorkerMain()","symbolLocation":140,"imageIndex":46},{"imageOffset":542560,"symbol":"void* std::_fl::__thread_proxy[abi:nn210000]<std::_fl::tuple<std::_fl::unique_ptr<std::_fl::__thread_struct, std::_fl::default_delete<std::_fl::__thread_struct>>, fml::ConcurrentMessageLoop::ConcurrentMessageLoop(unsigned long)::$_0>>(void*)","symbolLocation":212,"imageIndex":46},{"imageOffset":26096,"symbol":"_pthread_start","symbolLocation":104,"imageIndex":49},{"imageOffset":6552,"symbol":"thread_start","symbolLocation":8,"imageIndex":49}]},{"id":5931207,"name":"io.worker.4","threadState":{"x":[{"value":260},{"value":0},{"value":1024},{"value":0},{"value":0},{"value":160},{"value":0},{"value":0},{"value":6120877528},{"value":0},{"value":256},{"value":1099511628034},{"value":1099511628034},{"value":256},{"value":0},{"value":1099511628032},{"value":305},{"value":128},{"value":0},{"value":4410338344},{"value":4410338408},{"value":6120878304},{"value":0},{"value":0},{"value":1024},{"value":1024},{"value":1536},{"value":1},{"value":105553118564352}],"flavor":"ARM_THREAD_STATE64","lr":{"value":4382403256},"cpsr":{"value":1610616832},"fp":{"value":6120877648},"sp":{"value":6120877504},"esr":{"value":1442840704,"description":"(PC alignment)"},"pc":{"value":4390404128},"far":{"value":0}},"frames":[{"imageOffset":16416,"symbol":"__psynch_cvwait","symbolLocation":8,"imageIndex":48},{"imageOffset":27320,"symbol":"_pthread_cond_wait","symbolLocation":976,"imageIndex":49},{"imageOffset":411992,"symbol":"std::_fl::condition_variable::wait(std::_fl::unique_lock<std::_fl::mutex>&)","symbolLocation":24,"imageIndex":46},{"imageOffset":540212,"symbol":"fml::ConcurrentMessageLoop::WorkerMain()","symbolLocation":140,"imageIndex":46},{"imageOffset":542560,"symbol":"void* std::_fl::__thread_proxy[abi:nn210000]<std::_fl::tuple<std::_fl::unique_ptr<std::_fl::__thread_struct, std::_fl::default_delete<std::_fl::__thread_struct>>, fml::ConcurrentMessageLoop::ConcurrentMessageLoop(unsigned long)::$_0>>(void*)","symbolLocation":212,"imageIndex":46},{"imageOffset":26096,"symbol":"_pthread_start","symbolLocation":104,"imageIndex":49},{"imageOffset":6552,"symbol":"thread_start","symbolLocation":8,"imageIndex":49}]},{"id":5931208,"name":"dart:io EventHandler","threadState":{"x":[{"value":4},{"value":0},{"value":0},{"value":6121975064},{"value":16},{"value":6121974024},{"value":91259465126912},{"value":0},{"value":927000000},{"value":117},{"value":7696581396224},{"value":7696581396226},{"value":256},{"value":1},{"value":21504},{"value":21248},{"value":363},{"value":105553156119368},{"value":0},{"value":105553156120064},{"value":6121974024},{"value":67108864},{"value":2147483647},{"value":274877907},{"value":4294966296},{"value":1000000},{"value":209331455},{"value":0},{"value":0}],"flavor":"ARM_THREAD_STATE64","lr":{"value":4463320568},"cpsr":{"value":536875008},"fp":{"value":6121975664},"sp":{"value":6121974000},"esr":{"value":1442840704,"description":"(PC alignment)"},"pc":{"value":4390413916},"far":{"value":0}},"frames":[{"imageOffset":26204,"symbol":"kevent","symbolLocation":8,"imageIndex":48},{"imageOffset":5053944,"symbol":"dart::bin::EventHandlerImplementation::EventHandlerEntry(unsigned long)","symbolLocation":300,"imageIndex":46},{"imageOffset":5168976,"symbol":"dart::bin::ThreadStart(void*)","symbolLocation":88,"imageIndex":46},{"imageOffset":26096,"symbol":"_pthread_start","symbolLocation":104,"imageIndex":49},{"imageOffset":6552,"symbol":"thread_start","symbolLocation":8,"imageIndex":49}]},{"id":5931209,"name":"Dart Profiler ThreadInterrupter","threadState":{"x":[{"value":260},{"value":0},{"value":216320},{"value":0},{"value":0},{"value":160},{"value":0},{"value":0},{"value":6123072024},{"value":0},{"value":0},{"value":2},{"value":2},{"value":0},{"value":0},{"value":0},{"value":305},{"value":105553162564832},{"value":0},{"value":105553162564736},{"value":105553162564808},{"value":6123073760},{"value":0},{"value":0},{"value":216320},{"value":216577},{"value":216832},{"value":4494135296,"symbolLocation":3816,"symbol":"dart::Symbols::symbol_handles_"},{"value":0}],"flavor":"ARM_THREAD_STATE64","lr":{"value":4382403256},"cpsr":{"value":1610616832},"fp":{"value":6123072144},"sp":{"value":6123072000},"esr":{"value":1442840704,"description":"(PC alignment)"},"pc":{"value":4390404128},"far":{"value":0}},"frames":[{"imageOffset":16416,"symbol":"__psynch_cvwait","symbolLocation":8,"imageIndex":48},{"imageOffset":27320,"symbol":"_pthread_cond_wait","symbolLocation":976,"imageIndex":49},{"imageOffset":5477976,"symbol":"dart::ConditionVariable::WaitMicros(dart::Mutex*, long long)","symbolLocation":128,"imageIndex":46},{"imageOffset":6907380,"symbol":"dart::ThreadInterrupter::ThreadMain(unsigned long)","symbolLocation":328,"imageIndex":46},{"imageOffset":6610980,"symbol":"dart::ThreadStart(void*)","symbolLocation":204,"imageIndex":46},{"imageOffset":26096,"symbol":"_pthread_start","symbolLocation":104,"imageIndex":49},{"imageOffset":6552,"symbol":"thread_start","symbolLocation":8,"imageIndex":49}]},{"id":5931210,"name":"Dart Profiler SampleBlockProcessor","threadState":{"x":[{"value":260},{"value":0},{"value":256},{"value":0},{"value":0},{"value":160},{"value":0},{"value":100000000},{"value":5633},{"value":0},{"value":0},{"value":2},{"value":2},{"value":0},{"value":0},{"value":0},{"value":305},{"value":105553156254784},{"value":0},{"value":105553162564864},{"value":105553162564936},{"value":1},{"value":100000000},{"value":0},{"value":256},{"value":5633},{"value":5888},{"value":0},{"value":0}],"flavor":"ARM_THREAD_STATE64","lr":{"value":4382403300},"cpsr":{"value":2684358656},"fp":{"value":6124169872},"sp":{"value":6124169728},"esr":{"value":1442840704,"description":"(PC alignment)"},"pc":{"value":4390404128},"far":{"value":0}},"frames":[{"imageOffset":16416,"symbol":"__psynch_cvwait","symbolLocation":8,"imageIndex":48},{"imageOffset":27364,"symbol":"_pthread_cond_wait","symbolLocation":1020,"imageIndex":49},{"imageOffset":5477960,"symbol":"dart::ConditionVariable::WaitMicros(dart::Mutex*, long long)","symbolLocation":112,"imageIndex":46},{"imageOffset":6630252,"symbol":"dart::SampleBlockProcessor::ThreadMain(unsigned long)","symbolLocation":292,"imageIndex":46},{"imageOffset":6610980,"symbol":"dart::ThreadStart(void*)","symbolLocation":204,"imageIndex":46},{"imageOffset":26096,"symbol":"_pthread_start","symbolLocation":104,"imageIndex":49},{"imageOffset":6552,"symbol":"thread_start","symbolLocation":8,"imageIndex":49}]},{"id":5931211,"name":"DartWorker","threadState":{"x":[{"value":260},{"value":0},{"value":4864},{"value":0},{"value":0},{"value":160},{"value":5},{"value":0},{"value":4865},{"value":0},{"value":256},{"value":1099511628034},{"value":1099511628034},{"value":256},{"value":0},{"value":1099511628032},{"value":305},{"value":196},{"value":0},{"value":4411443432},{"value":105553156255408},{"value":1},{"value":0},{"value":5},{"value":4864},{"value":4865},{"value":5120},{"value":4494135296,"symbolLocation":3816,"symbol":"dart::Symbols::symbol_handles_"},{"value":1000}],"flavor":"ARM_THREAD_STATE64","lr":{"value":4382403300},"cpsr":{"value":2684358656},"fp":{"value":6125267520},"sp":{"value":6125267376},"esr":{"value":1442840704,"description":"(PC alignment)"},"pc":{"value":4390404128},"far":{"value":0}},"frames":[{"imageOffset":16416,"symbol":"__psynch_cvwait","symbolLocation":8,"imageIndex":48},{"imageOffset":27364,"symbol":"_pthread_cond_wait","symbolLocation":1020,"imageIndex":49},{"imageOffset":5477960,"symbol":"dart::ConditionVariable::WaitMicros(dart::Mutex*, long long)","symbolLocation":112,"imageIndex":46},{"imageOffset":6910828,"symbol":"dart::ThreadPool::WorkerLoop(dart::ThreadPool::Worker*)","symbolLocation":508,"imageIndex":46},{"imageOffset":6911172,"symbol":"dart::ThreadPool::Worker::Main(unsigned long)","symbolLocation":116,"imageIndex":46},{"imageOffset":6610980,"symbol":"dart::ThreadStart(void*)","symbolLocation":204,"imageIndex":46},{"imageOffset":26096,"symbol":"_pthread_start","symbolLocation":104,"imageIndex":49},{"imageOffset":6552,"symbol":"thread_start","symbolLocation":8,"imageIndex":49}]},{"id":5931275,"name":"DartWorker","threadState":{"x":[{"value":260},{"value":0},{"value":512},{"value":0},{"value":0},{"value":160},{"value":5},{"value":0},{"value":513},{"value":0},{"value":0},{"value":2},{"value":2},{"value":0},{"value":0},{"value":0},{"value":305},{"value":81},{"value":0},{"value":4506796200},{"value":105553156210736},{"value":1},{"value":0},{"value":5},{"value":512},{"value":513},{"value":768},{"value":4494135296,"symbolLocation":3816,"symbol":"dart::Symbols::symbol_handles_"},{"value":1000}],"flavor":"ARM_THREAD_STATE64","lr":{"value":4382403300},"cpsr":{"value":2684358656},"fp":{"value":6126365248},"sp":{"value":6126365104},"esr":{"value":1442840704,"description":"(PC alignment)"},"pc":{"value":4390404128},"far":{"value":0}},"frames":[{"imageOffset":16416,"symbol":"__psynch_cvwait","symbolLocation":8,"imageIndex":48},{"imageOffset":27364,"symbol":"_pthread_cond_wait","symbolLocation":1020,"imageIndex":49},{"imageOffset":5477960,"symbol":"dart::ConditionVariable::WaitMicros(dart::Mutex*, long long)","symbolLocation":112,"imageIndex":46},{"imageOffset":6910828,"symbol":"dart::ThreadPool::WorkerLoop(dart::ThreadPool::Worker*)","symbolLocation":508,"imageIndex":46},{"imageOffset":6911172,"symbol":"dart::ThreadPool::Worker::Main(unsigned long)","symbolLocation":116,"imageIndex":46},{"imageOffset":6610980,"symbol":"dart::ThreadStart(void*)","symbolLocation":204,"imageIndex":46},{"imageOffset":26096,"symbol":"_pthread_start","symbolLocation":104,"imageIndex":49},{"imageOffset":6552,"symbol":"thread_start","symbolLocation":8,"imageIndex":49}]},{"id":5931276,"name":"DartWorker","threadState":{"x":[{"value":260},{"value":0},{"value":21504},{"value":0},{"value":0},{"value":160},{"value":61},{"value":0},{"value":21505},{"value":0},{"value":0},{"value":2},{"value":2},{"value":0},{"value":0},{"value":0},{"value":305},{"value":507},{"value":0},{"value":4395652040},{"value":105553156119344},{"value":1},{"value":0},{"value":61},{"value":21504},{"value":21505},{"value":21760},{"value":4494135296,"symbolLocation":3816,"symbol":"dart::Symbols::symbol_handles_"},{"value":1000}],"flavor":"ARM_THREAD_STATE64","lr":{"value":4382403300},"cpsr":{"value":2684358656},"fp":{"value":6127462896},"sp":{"value":6127462752},"esr":{"value":1442840704,"description":"(PC alignment)"},"pc":{"value":4390404128},"far":{"value":0}},"frames":[{"imageOffset":16416,"symbol":"__psynch_cvwait","symbolLocation":8,"imageIndex":48},{"imageOffset":27364,"symbol":"_pthread_cond_wait","symbolLocation":1020,"imageIndex":49},{"imageOffset":5477960,"symbol":"dart::ConditionVariable::WaitMicros(dart::Mutex*, long long)","symbolLocation":112,"imageIndex":46},{"imageOffset":5863564,"symbol":"dart::MutatorThreadPool::OnEnterIdleLocked(dart::MutexLocker*, dart::ThreadPool::Worker*)","symbolLocation":152,"imageIndex":46},{"imageOffset":6910444,"symbol":"dart::ThreadPool::WorkerLoop(dart::ThreadPool::Worker*)","symbolLocation":124,"imageIndex":46},{"imageOffset":6911172,"symbol":"dart::ThreadPool::Worker::Main(unsigned long)","symbolLocation":116,"imageIndex":46},{"imageOffset":6610980,"symbol":"dart::ThreadStart(void*)","symbolLocation":204,"imageIndex":46},{"imageOffset":26096,"symbol":"_pthread_start","symbolLocation":104,"imageIndex":49},{"imageOffset":6552,"symbol":"thread_start","symbolLocation":8,"imageIndex":49}]},{"id":5931379,"name":"DartWorker","threadState":{"x":[{"value":260},{"value":0},{"value":0},{"value":0},{"value":0},{"value":160},{"value":5},{"value":0},{"value":1},{"value":0},{"value":0},{"value":2},{"value":2},{"value":0},{"value":0},{"value":0},{"value":305},{"value":3417846010},{"value":0},{"value":4395652040},{"value":105553156186608},{"value":1},{"value":0},{"value":5},{"value":0},{"value":1},{"value":256},{"value":4494135296,"symbolLocation":3816,"symbol":"dart::Symbols::symbol_handles_"},{"value":1000}],"flavor":"ARM_THREAD_STATE64","lr":{"value":4382403300},"cpsr":{"value":2684358656},"fp":{"value":6128560704},"sp":{"value":6128560560},"esr":{"value":1442840704,"description":"(PC alignment)"},"pc":{"value":4390404128},"far":{"value":0}},"frames":[{"imageOffset":16416,"symbol":"__psynch_cvwait","symbolLocation":8,"imageIndex":48},{"imageOffset":27364,"symbol":"_pthread_cond_wait","symbolLocation":1020,"imageIndex":49},{"imageOffset":5477960,"symbol":"dart::ConditionVariable::WaitMicros(dart::Mutex*, long long)","symbolLocation":112,"imageIndex":46},{"imageOffset":6910828,"symbol":"dart::ThreadPool::WorkerLoop(dart::ThreadPool::Worker*)","symbolLocation":508,"imageIndex":46},{"imageOffset":6911172,"symbol":"dart::ThreadPool::Worker::Main(unsigned long)","symbolLocation":116,"imageIndex":46},{"imageOffset":6610980,"symbol":"dart::ThreadStart(void*)","symbolLocation":204,"imageIndex":46},{"imageOffset":26096,"symbol":"_pthread_start","symbolLocation":104,"imageIndex":49},{"imageOffset":6552,"symbol":"thread_start","symbolLocation":8,"imageIndex":49}]},{"id":5931406,"name":"caulk.messenger.shared:17","threadState":{"x":[{"value":14},{"value":105553118831962},{"value":0},{"value":6129135722},{"value":105553118831936},{"value":25},{"value":0},{"value":0},{"value":0},{"value":4294967295},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":18446744073709551580},{"value":0},{"value":0},{"value":105553176803584},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0}],"flavor":"ARM_THREAD_STATE64","lr":{"value":7360289596},"cpsr":{"value":2147487744},"fp":{"value":6129135488},"sp":{"value":6129135456},"esr":{"value":1442840704,"description":"(PC alignment)"},"pc":{"value":4390390508},"far":{"value":0}},"frames":[{"imageOffset":2796,"symbol":"semaphore_wait_trap","symbolLocation":8,"imageIndex":48},{"imageOffset":64316,"symbol":"caulk::semaphore::timed_wait(double)","symbolLocation":220,"imageIndex":63},{"imageOffset":93248,"symbol":"caulk::concurrent::details::worker_thread::run()","symbolLocation":28,"imageIndex":63},{"imageOffset":93364,"symbol":"void* caulk::thread_proxy<std::__1::tuple<caulk::thread::attributes, void (caulk::concurrent::details::worker_thread::*)(), std::__1::tuple<caulk::concurrent::details::worker_thread*>>>(void*)","symbolLocation":48,"imageIndex":63},{"imageOffset":26096,"symbol":"_pthread_start","symbolLocation":104,"imageIndex":49},{"imageOffset":6552,"symbol":"thread_start","symbolLocation":8,"imageIndex":49}]},{"id":5931407,"name":"caulk.messenger.shared:high","threadState":{"x":[{"value":14},{"value":105553118495836},{"value":0},{"value":6129709164},{"value":105553118495808},{"value":27},{"value":0},{"value":0},{"value":0},{"value":4294967295},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":18446744073709551580},{"value":0},{"value":0},{"value":105553176150464},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0}],"flavor":"ARM_THREAD_STATE64","lr":{"value":7360289596},"cpsr":{"value":2147487744},"fp":{"value":6129708928},"sp":{"value":6129708896},"esr":{"value":1442840704,"description":"(PC alignment)"},"pc":{"value":4390390508},"far":{"value":0}},"frames":[{"imageOffset":2796,"symbol":"semaphore_wait_trap","symbolLocation":8,"imageIndex":48},{"imageOffset":64316,"symbol":"caulk::semaphore::timed_wait(double)","symbolLocation":220,"imageIndex":63},{"imageOffset":93248,"symbol":"caulk::concurrent::details::worker_thread::run()","symbolLocation":28,"imageIndex":63},{"imageOffset":93364,"symbol":"void* caulk::thread_proxy<std::__1::tuple<caulk::thread::attributes, void (caulk::concurrent::details::worker_thread::*)(), std::__1::tuple<caulk::concurrent::details::worker_thread*>>>(void*)","symbolLocation":48,"imageIndex":63},{"imageOffset":26096,"symbol":"_pthread_start","symbolLocation":104,"imageIndex":49},{"imageOffset":6552,"symbol":"thread_start","symbolLocation":8,"imageIndex":49}]},{"id":5931429,"name":"DartWorker","threadState":{"x":[{"value":260},{"value":0},{"value":4864},{"value":0},{"value":0},{"value":160},{"value":5},{"value":0},{"value":4865},{"value":0},{"value":256},{"value":1099511628034},{"value":1099511628034},{"value":256},{"value":0},{"value":1099511628032},{"value":305},{"value":383},{"value":0},{"value":4411443432},{"value":105553156229616},{"value":1},{"value":0},{"value":5},{"value":4864},{"value":4865},{"value":5120},{"value":4494135296,"symbolLocation":3816,"symbol":"dart::Symbols::symbol_handles_"},{"value":1000}],"flavor":"ARM_THREAD_STATE64","lr":{"value":4382403300},"cpsr":{"value":2684358656},"fp":{"value":6130805312},"sp":{"value":6130805168},"esr":{"value":1442840704,"description":"(PC alignment)"},"pc":{"value":4390404128},"far":{"value":0}},"frames":[{"imageOffset":16416,"symbol":"__psynch_cvwait","symbolLocation":8,"imageIndex":48},{"imageOffset":27364,"symbol":"_pthread_cond_wait","symbolLocation":1020,"imageIndex":49},{"imageOffset":5477960,"symbol":"dart::ConditionVariable::WaitMicros(dart::Mutex*, long long)","symbolLocation":112,"imageIndex":46},{"imageOffset":6910828,"symbol":"dart::ThreadPool::WorkerLoop(dart::ThreadPool::Worker*)","symbolLocation":508,"imageIndex":46},{"imageOffset":6911172,"symbol":"dart::ThreadPool::Worker::Main(unsigned long)","symbolLocation":116,"imageIndex":46},{"imageOffset":6610980,"symbol":"dart::ThreadStart(void*)","symbolLocation":204,"imageIndex":46},{"imageOffset":26096,"symbol":"_pthread_start","symbolLocation":104,"imageIndex":49},{"imageOffset":6552,"symbol":"thread_start","symbolLocation":8,"imageIndex":49}]}],
  "usedImages" : [
  {
    "source" : "P",
    "arch" : "arm64e",
    "base" : 4367761408,
    "size" : 655360,
    "uuid" : "ff357121-622e-3ea0-9ef0-8d706f44dc1a",
    "path" : "\/usr\/lib\/dyld",
    "name" : "dyld"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4366188544,
    "CFBundleShortVersionString" : "1.0.0",
    "CFBundleIdentifier" : "com.hopenapp.hopen",
    "size" : 16384,
    "uuid" : "caf7731a-2c8a-3262-ac04-f72f06f73eab",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/24942F86-3060-4B2A-962B-60BD9614F1F4\/data\/Containers\/Bundle\/Application\/13FF1FEF-7257-49BB-8287-1FCD0810CD0E\/Runner.app\/Runner",
    "name" : "Runner",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4369776640,
    "size" : 540672,
    "uuid" : "c14c9eb6-9864-3bfd-b8df-604b8f776e52",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/24942F86-3060-4B2A-962B-60BD9614F1F4\/data\/Containers\/Bundle\/Application\/13FF1FEF-7257-49BB-8287-1FCD0810CD0E\/Runner.app\/Runner.debug.dylib",
    "name" : "Runner.debug.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4366680064,
    "CFBundleShortVersionString" : "1.7.6",
    "CFBundleIdentifier" : "org.cocoapods.AppAuth",
    "size" : 163840,
    "uuid" : "cd329467-4e64-33fd-8f81-c140b9c607a9",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/24942F86-3060-4B2A-962B-60BD9614F1F4\/data\/Containers\/Bundle\/Application\/13FF1FEF-7257-49BB-8287-1FCD0810CD0E\/Runner.app\/Frameworks\/AppAuth.framework\/AppAuth",
    "name" : "AppAuth",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4367040512,
    "CFBundleShortVersionString" : "11.2.0",
    "CFBundleIdentifier" : "org.cocoapods.AppCheckCore",
    "size" : 147456,
    "uuid" : "31ce7028-5a68-39f1-a840-891714831d69",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/24942F86-3060-4B2A-962B-60BD9614F1F4\/data\/Containers\/Bundle\/Application\/13FF1FEF-7257-49BB-8287-1FCD0810CD0E\/Runner.app\/Frameworks\/AppCheckCore.framework\/AppCheckCore",
    "name" : "AppCheckCore",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4372381696,
    "CFBundleShortVersionString" : "4.3.9",
    "CFBundleIdentifier" : "org.cocoapods.DKImagePickerController",
    "size" : 638976,
    "uuid" : "03fcf312-d00e-3900-9646-92f66cbdaf51",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/24942F86-3060-4B2A-962B-60BD9614F1F4\/data\/Containers\/Bundle\/Application\/13FF1FEF-7257-49BB-8287-1FCD0810CD0E\/Runner.app\/Frameworks\/DKImagePickerController.framework\/DKImagePickerController",
    "name" : "DKImagePickerController",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4373954560,
    "CFBundleShortVersionString" : "0.0.19",
    "CFBundleIdentifier" : "org.cocoapods.DKPhotoGallery",
    "size" : 557056,
    "uuid" : "9cc3f835-0645-3b4c-962e-78404b6674c6",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/24942F86-3060-4B2A-962B-60BD9614F1F4\/data\/Containers\/Bundle\/Application\/13FF1FEF-7257-49BB-8287-1FCD0810CD0E\/Runner.app\/Frameworks\/DKPhotoGallery.framework\/DKPhotoGallery",
    "name" : "DKPhotoGallery",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4367400960,
    "CFBundleShortVersionString" : "2.4.0",
    "CFBundleIdentifier" : "org.cocoapods.FBLPromises",
    "size" : 81920,
    "uuid" : "962c6d34-cde2-3ecf-ba9b-3af78f34ab5a",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/24942F86-3060-4B2A-962B-60BD9614F1F4\/data\/Containers\/Bundle\/Application\/13FF1FEF-7257-49BB-8287-1FCD0810CD0E\/Runner.app\/Frameworks\/FBLPromises.framework\/FBLPromises",
    "name" : "FBLPromises",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4370808832,
    "CFBundleShortVersionString" : "11.15.0",
    "CFBundleIdentifier" : "org.cocoapods.FirebaseCore",
    "size" : 81920,
    "uuid" : "5e0d33ee-5834-3d02-972b-27a987c049e3",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/24942F86-3060-4B2A-962B-60BD9614F1F4\/data\/Containers\/Bundle\/Application\/13FF1FEF-7257-49BB-8287-1FCD0810CD0E\/Runner.app\/Frameworks\/FirebaseCore.framework\/FirebaseCore",
    "name" : "FirebaseCore",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4371529728,
    "CFBundleShortVersionString" : "11.15.0",
    "CFBundleIdentifier" : "org.cocoapods.FirebaseCoreInternal",
    "size" : 163840,
    "uuid" : "29efc00f-307c-3f23-80fa-534b89bd3c78",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/24942F86-3060-4B2A-962B-60BD9614F1F4\/data\/Containers\/Bundle\/Application\/13FF1FEF-7257-49BB-8287-1FCD0810CD0E\/Runner.app\/Frameworks\/FirebaseCoreInternal.framework\/FirebaseCoreInternal",
    "name" : "FirebaseCoreInternal",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4371021824,
    "CFBundleShortVersionString" : "11.15.0",
    "CFBundleIdentifier" : "org.cocoapods.FirebaseInstallations",
    "size" : 114688,
    "uuid" : "d9fbd69a-5b18-38a7-adc4-499fc338c58c",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/24942F86-3060-4B2A-962B-60BD9614F1F4\/data\/Containers\/Bundle\/Application\/13FF1FEF-7257-49BB-8287-1FCD0810CD0E\/Runner.app\/Frameworks\/FirebaseInstallations.framework\/FirebaseInstallations",
    "name" : "FirebaseInstallations",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4375855104,
    "CFBundleShortVersionString" : "11.15.0",
    "CFBundleIdentifier" : "org.cocoapods.FirebaseMessaging",
    "size" : 294912,
    "uuid" : "2decb982-fd1b-3698-ade5-50d8b05739d2",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/24942F86-3060-4B2A-962B-60BD9614F1F4\/data\/Containers\/Bundle\/Application\/13FF1FEF-7257-49BB-8287-1FCD0810CD0E\/Runner.app\/Frameworks\/FirebaseMessaging.framework\/FirebaseMessaging",
    "name" : "FirebaseMessaging",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4366303232,
    "CFBundleShortVersionString" : "4.1.1",
    "CFBundleIdentifier" : "org.cocoapods.GTMAppAuth",
    "size" : 147456,
    "uuid" : "b56e79ea-28fb-3a59-b431-e8fc09b71b48",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/24942F86-3060-4B2A-962B-60BD9614F1F4\/data\/Containers\/Bundle\/Application\/13FF1FEF-7257-49BB-8287-1FCD0810CD0E\/Runner.app\/Frameworks\/GTMAppAuth.framework\/GTMAppAuth",
    "name" : "GTMAppAuth",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4377100288,
    "CFBundleShortVersionString" : "3.5.0",
    "CFBundleIdentifier" : "org.cocoapods.GTMSessionFetcher",
    "size" : 360448,
    "uuid" : "205fdd37-40d9-38fe-b575-614ac31c7140",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/24942F86-3060-4B2A-962B-60BD9614F1F4\/data\/Containers\/Bundle\/Application\/13FF1FEF-7257-49BB-8287-1FCD0810CD0E\/Runner.app\/Frameworks\/GTMSessionFetcher.framework\/GTMSessionFetcher",
    "name" : "GTMSessionFetcher",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4376444928,
    "CFBundleShortVersionString" : "10.1.0",
    "CFBundleIdentifier" : "org.cocoapods.GoogleDataTransport",
    "size" : 212992,
    "uuid" : "a5c335de-684e-3b83-ba64-e21e447ea889",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/24942F86-3060-4B2A-962B-60BD9614F1F4\/data\/Containers\/Bundle\/Application\/13FF1FEF-7257-49BB-8287-1FCD0810CD0E\/Runner.app\/Frameworks\/GoogleDataTransport.framework\/GoogleDataTransport",
    "name" : "GoogleDataTransport",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4375248896,
    "CFBundleShortVersionString" : "8.0.0",
    "CFBundleIdentifier" : "org.cocoapods.GoogleSignIn",
    "size" : 147456,
    "uuid" : "55fb2deb-d907-305e-a508-ee748f35ff18",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/24942F86-3060-4B2A-962B-60BD9614F1F4\/data\/Containers\/Bundle\/Application\/13FF1FEF-7257-49BB-8287-1FCD0810CD0E\/Runner.app\/Frameworks\/GoogleSignIn.framework\/GoogleSignIn",
    "name" : "GoogleSignIn",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4377739264,
    "CFBundleShortVersionString" : "8.1.0",
    "CFBundleIdentifier" : "org.cocoapods.GoogleUtilities",
    "size" : 147456,
    "uuid" : "579d30f5-873d-3cac-afaa-2ef68808afd3",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/24942F86-3060-4B2A-962B-60BD9614F1F4\/data\/Containers\/Bundle\/Application\/13FF1FEF-7257-49BB-8287-1FCD0810CD0E\/Runner.app\/Frameworks\/GoogleUtilities.framework\/GoogleUtilities",
    "name" : "GoogleUtilities",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4371300352,
    "CFBundleShortVersionString" : "2.2.0",
    "CFBundleIdentifier" : "org.cocoapods.Mantle",
    "size" : 114688,
    "uuid" : "6b831635-7679-349a-8447-49703eebba25",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/24942F86-3060-4B2A-962B-60BD9614F1F4\/data\/Containers\/Bundle\/Application\/13FF1FEF-7257-49BB-8287-1FCD0810CD0E\/Runner.app\/Frameworks\/Mantle.framework\/Mantle",
    "name" : "Mantle",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4383227904,
    "CFBundleShortVersionString" : "3.29.0",
    "CFBundleIdentifier" : "org.cocoapods.PostHog",
    "size" : 2310144,
    "uuid" : "780581b9-7198-311c-820d-77468c8d52c9",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/24942F86-3060-4B2A-962B-60BD9614F1F4\/data\/Containers\/Bundle\/Application\/13FF1FEF-7257-49BB-8287-1FCD0810CD0E\/Runner.app\/Frameworks\/PostHog.framework\/PostHog",
    "name" : "PostHog",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4375592960,
    "CFBundleShortVersionString" : "5.2.4",
    "CFBundleIdentifier" : "org.cocoapods.Reachability",
    "size" : 49152,
    "uuid" : "d5570c0a-e256-3e20-8732-c2b425cc2bce",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/24942F86-3060-4B2A-962B-60BD9614F1F4\/data\/Containers\/Bundle\/Application\/13FF1FEF-7257-49BB-8287-1FCD0810CD0E\/Runner.app\/Frameworks\/Reachability.framework\/Reachability",
    "name" : "Reachability",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4379099136,
    "CFBundleShortVersionString" : "5.21.1",
    "CFBundleIdentifier" : "org.cocoapods.SDWebImage",
    "size" : 524288,
    "uuid" : "43368b18-84f3-3020-bd85-9da338c90e8a",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/24942F86-3060-4B2A-962B-60BD9614F1F4\/data\/Containers\/Bundle\/Application\/13FF1FEF-7257-49BB-8287-1FCD0810CD0E\/Runner.app\/Frameworks\/SDWebImage.framework\/SDWebImage",
    "name" : "SDWebImage",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4367613952,
    "CFBundleShortVersionString" : "0.14.6",
    "CFBundleIdentifier" : "org.cocoapods.SDWebImageWebPCoder",
    "size" : 49152,
    "uuid" : "070de3a9-68cf-36c8-965a-e06b41c095bc",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/24942F86-3060-4B2A-962B-60BD9614F1F4\/data\/Containers\/Bundle\/Application\/13FF1FEF-7257-49BB-8287-1FCD0810CD0E\/Runner.app\/Frameworks\/SDWebImageWebPCoder.framework\/SDWebImageWebPCoder",
    "name" : "SDWebImageWebPCoder",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4378050560,
    "CFBundleShortVersionString" : "5.4.5",
    "CFBundleIdentifier" : "org.cocoapods.SwiftyGif",
    "size" : 98304,
    "uuid" : "88cedd7c-e176-32e9-aaa4-99c7c8e78da9",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/24942F86-3060-4B2A-962B-60BD9614F1F4\/data\/Containers\/Bundle\/Application\/13FF1FEF-7257-49BB-8287-1FCD0810CD0E\/Runner.app\/Frameworks\/SwiftyGif.framework\/SwiftyGif",
    "name" : "SwiftyGif",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4398989312,
    "CFBundleShortVersionString" : "1.0",
    "CFBundleIdentifier" : "org.webrtc.WebRTC",
    "size" : 9666560,
    "uuid" : "4c4c4404-5555-3144-a1e8-358e2ad27d7b",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/24942F86-3060-4B2A-962B-60BD9614F1F4\/data\/Containers\/Bundle\/Application\/13FF1FEF-7257-49BB-8287-1FCD0810CD0E\/Runner.app\/Frameworks\/WebRTC.framework\/WebRTC",
    "name" : "WebRTC",
    "CFBundleVersion" : "1.0"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4372021248,
    "CFBundleShortVersionString" : "0.0.1",
    "CFBundleIdentifier" : "org.cocoapods.audioplayers-darwin",
    "size" : 131072,
    "uuid" : "5dba94ce-5e49-3df4-a5dc-1b20006850a3",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/24942F86-3060-4B2A-962B-60BD9614F1F4\/data\/Containers\/Bundle\/Application\/13FF1FEF-7257-49BB-8287-1FCD0810CD0E\/Runner.app\/Frameworks\/audioplayers_darwin.framework\/audioplayers_darwin",
    "name" : "audioplayers_darwin",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4378329088,
    "CFBundleShortVersionString" : "1.3.8",
    "CFBundleIdentifier" : "org.cocoapods.background-fetch",
    "size" : 49152,
    "uuid" : "3082c5bb-a16c-31e6-8582-dcdb7654da58",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/24942F86-3060-4B2A-962B-60BD9614F1F4\/data\/Containers\/Bundle\/Application\/13FF1FEF-7257-49BB-8287-1FCD0810CD0E\/Runner.app\/Frameworks\/background_fetch.framework\/background_fetch",
    "name" : "background_fetch",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4380884992,
    "CFBundleShortVersionString" : "0.0.1",
    "CFBundleIdentifier" : "org.cocoapods.camera-avfoundation",
    "size" : 327680,
    "uuid" : "57779bd3-e5f9-39e2-8a94-6cc8e13eb4db",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/24942F86-3060-4B2A-962B-60BD9614F1F4\/data\/Containers\/Bundle\/Application\/13FF1FEF-7257-49BB-8287-1FCD0810CD0E\/Runner.app\/Frameworks\/camera_avfoundation.framework\/camera_avfoundation",
    "name" : "camera_avfoundation",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4378476544,
    "CFBundleShortVersionString" : "0.0.1",
    "CFBundleIdentifier" : "org.cocoapods.connectivity-plus",
    "size" : 49152,
    "uuid" : "a9267679-a863-3c9c-86b8-421fa69f8fdd",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/24942F86-3060-4B2A-962B-60BD9614F1F4\/data\/Containers\/Bundle\/Application\/13FF1FEF-7257-49BB-8287-1FCD0810CD0E\/Runner.app\/Frameworks\/connectivity_plus.framework\/connectivity_plus",
    "name" : "connectivity_plus",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4378656768,
    "CFBundleShortVersionString" : "0.0.1",
    "CFBundleIdentifier" : "org.cocoapods.cupertino-http",
    "size" : 65536,
    "uuid" : "c534a179-fcdb-3f80-b7d3-00ce32507584",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/24942F86-3060-4B2A-962B-60BD9614F1F4\/data\/Containers\/Bundle\/Application\/13FF1FEF-7257-49BB-8287-1FCD0810CD0E\/Runner.app\/Frameworks\/cupertino_http.framework\/cupertino_http",
    "name" : "cupertino_http",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4378820608,
    "CFBundleShortVersionString" : "0.0.1",
    "CFBundleIdentifier" : "org.cocoapods.file-picker",
    "size" : 81920,
    "uuid" : "c0502278-71ed-3e27-ade8-7c16e317672e",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/24942F86-3060-4B2A-962B-60BD9614F1F4\/data\/Containers\/Bundle\/Application\/13FF1FEF-7257-49BB-8287-1FCD0810CD0E\/Runner.app\/Frameworks\/file_picker.framework\/file_picker",
    "name" : "file_picker",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4381622272,
    "CFBundleShortVersionString" : "1.0.0",
    "CFBundleIdentifier" : "org.cocoapods.flutter-image-compress-common",
    "size" : 147456,
    "uuid" : "e0b45e4d-d6f6-3269-ba72-505c1df8f371",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/24942F86-3060-4B2A-962B-60BD9614F1F4\/data\/Containers\/Bundle\/Application\/13FF1FEF-7257-49BB-8287-1FCD0810CD0E\/Runner.app\/Frameworks\/flutter_image_compress_common.framework\/flutter_image_compress_common",
    "name" : "flutter_image_compress_common",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4380131328,
    "CFBundleShortVersionString" : "0.0.1",
    "CFBundleIdentifier" : "org.cocoapods.flutter-local-notifications",
    "size" : 65536,
    "uuid" : "a3de282d-4d89-34ed-b74c-6a0c45a157aa",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/24942F86-3060-4B2A-962B-60BD9614F1F4\/data\/Containers\/Bundle\/Application\/13FF1FEF-7257-49BB-8287-1FCD0810CD0E\/Runner.app\/Frameworks\/flutter_local_notifications.framework\/flutter_local_notifications",
    "name" : "flutter_local_notifications",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4380295168,
    "CFBundleShortVersionString" : "6.0.0",
    "CFBundleIdentifier" : "org.cocoapods.flutter-secure-storage",
    "size" : 65536,
    "uuid" : "d709d26f-6cb7-3452-91d8-5ab0d96da42e",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/24942F86-3060-4B2A-962B-60BD9614F1F4\/data\/Containers\/Bundle\/Application\/13FF1FEF-7257-49BB-8287-1FCD0810CD0E\/Runner.app\/Frameworks\/flutter_secure_storage.framework\/flutter_secure_storage",
    "name" : "flutter_secure_storage",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4376887296,
    "CFBundleShortVersionString" : "0.0.1",
    "CFBundleIdentifier" : "org.cocoapods.image-picker-ios",
    "size" : 81920,
    "uuid" : "d8c14994-b67e-3089-ad07-ef19b43fdc0b",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/24942F86-3060-4B2A-962B-60BD9614F1F4\/data\/Containers\/Bundle\/Application\/13FF1FEF-7257-49BB-8287-1FCD0810CD0E\/Runner.app\/Frameworks\/image_picker_ios.framework\/image_picker_ios",
    "name" : "image_picker_ios",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4388438016,
    "CFBundleShortVersionString" : "1.5.0",
    "CFBundleIdentifier" : "org.cocoapods.libwebp",
    "size" : 655360,
    "uuid" : "ac0f2aff-6f25-3923-951f-5204e2d38d98",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/24942F86-3060-4B2A-962B-60BD9614F1F4\/data\/Containers\/Bundle\/Application\/13FF1FEF-7257-49BB-8287-1FCD0810CD0E\/Runner.app\/Frameworks\/libwebp.framework\/libwebp",
    "name" : "libwebp",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4379000832,
    "CFBundleShortVersionString" : "3.30910.0",
    "CFBundleIdentifier" : "org.cocoapods.nanopb",
    "size" : 32768,
    "uuid" : "6762a519-8c26-374b-9732-572f22a294a8",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/24942F86-3060-4B2A-962B-60BD9614F1F4\/data\/Containers\/Bundle\/Application\/13FF1FEF-7257-49BB-8287-1FCD0810CD0E\/Runner.app\/Frameworks\/nanopb.framework\/nanopb",
    "name" : "nanopb",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4380639232,
    "CFBundleShortVersionString" : "0.0.1",
    "CFBundleIdentifier" : "org.cocoapods.objective-c",
    "size" : 32768,
    "uuid" : "0d8a2c56-fdbb-3c89-803a-4f866892c0b2",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/24942F86-3060-4B2A-962B-60BD9614F1F4\/data\/Containers\/Bundle\/Application\/13FF1FEF-7257-49BB-8287-1FCD0810CD0E\/Runner.app\/Frameworks\/objective_c.framework\/objective_c",
    "name" : "objective_c",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : **********,
    "CFBundleShortVersionString" : "0.0.1",
    "CFBundleIdentifier" : "org.cocoapods.path-provider-foundation",
    "size" : 49152,
    "uuid" : "ea818c3f-c528-3db6-9e33-cc9232a2234a",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/24942F86-3060-4B2A-962B-60BD9614F1F4\/data\/Containers\/Bundle\/Application\/13FF1FEF-7257-49BB-8287-1FCD0810CD0E\/Runner.app\/Frameworks\/path_provider_foundation.framework\/path_provider_foundation",
    "name" : "path_provider_foundation",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : **********,
    "CFBundleShortVersionString" : "0.0.1",
    "CFBundleIdentifier" : "org.cocoapods.posthog-flutter",
    "size" : 65536,
    "uuid" : "05eb2d96-d712-30ef-abfb-0c9e0bd2e106",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/24942F86-3060-4B2A-962B-60BD9614F1F4\/data\/Containers\/Bundle\/Application\/13FF1FEF-7257-49BB-8287-1FCD0810CD0E\/Runner.app\/Frameworks\/posthog_flutter.framework\/posthog_flutter",
    "name" : "posthog_flutter",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : **********,
    "CFBundleShortVersionString" : "1.0.0",
    "CFBundleIdentifier" : "org.cocoapods.record-ios",
    "size" : 147456,
    "uuid" : "4fb8fc2b-1094-37ca-aa9a-c48d9937c6de",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/24942F86-3060-4B2A-962B-60BD9614F1F4\/data\/Containers\/Bundle\/Application\/13FF1FEF-7257-49BB-8287-1FCD0810CD0E\/Runner.app\/Frameworks\/record_ios.framework\/record_ios",
    "name" : "record_ios",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4389404672,
    "CFBundleShortVersionString" : "0.0.1",
    "CFBundleIdentifier" : "org.cocoapods.shared-preferences-foundation",
    "size" : 81920,
    "uuid" : "7f787c28-8ab8-38d1-9f4a-9c0acc03d294",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/24942F86-3060-4B2A-962B-60BD9614F1F4\/data\/Containers\/Bundle\/Application\/13FF1FEF-7257-49BB-8287-1FCD0810CD0E\/Runner.app\/Frameworks\/shared_preferences_foundation.framework\/shared_preferences_foundation",
    "name" : "shared_preferences_foundation",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4389634048,
    "CFBundleShortVersionString" : "0.0.1",
    "CFBundleIdentifier" : "org.cocoapods.sign-in-with-apple",
    "size" : 81920,
    "uuid" : "6a97bc70-a8c9-3481-bc7f-0c94463d6fc7",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/24942F86-3060-4B2A-962B-60BD9614F1F4\/data\/Containers\/Bundle\/Application\/13FF1FEF-7257-49BB-8287-1FCD0810CD0E\/Runner.app\/Frameworks\/sign_in_with_apple.framework\/sign_in_with_apple",
    "name" : "sign_in_with_apple",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4389863424,
    "CFBundleShortVersionString" : "0.0.4",
    "CFBundleIdentifier" : "org.cocoapods.sqflite-darwin",
    "size" : 131072,
    "uuid" : "8089bff1-2204-3398-b695-4d26b27ed341",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/24942F86-3060-4B2A-962B-60BD9614F1F4\/data\/Containers\/Bundle\/Application\/13FF1FEF-7257-49BB-8287-1FCD0810CD0E\/Runner.app\/Frameworks\/sqflite_darwin.framework\/sqflite_darwin",
    "name" : "sqflite_darwin",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4390158336,
    "CFBundleShortVersionString" : "0.0.1",
    "CFBundleIdentifier" : "org.cocoapods.video-player-avfoundation",
    "size" : 98304,
    "uuid" : "c69d2e76-b993-3733-b43c-444bb697ef2b",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/24942F86-3060-4B2A-962B-60BD9614F1F4\/data\/Containers\/Bundle\/Application\/13FF1FEF-7257-49BB-8287-1FCD0810CD0E\/Runner.app\/Frameworks\/video_player_avfoundation.framework\/video_player_avfoundation",
    "name" : "video_player_avfoundation",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4392157184,
    "CFBundleShortVersionString" : "0.0.1",
    "CFBundleIdentifier" : "org.cocoapods.webview-flutter-wkwebview",
    "size" : 704512,
    "uuid" : "ec39c002-5a1d-3a12-a28b-6f9ab7cfcbb4",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/24942F86-3060-4B2A-962B-60BD9614F1F4\/data\/Containers\/Bundle\/Application\/13FF1FEF-7257-49BB-8287-1FCD0810CD0E\/Runner.app\/Frameworks\/webview_flutter_wkwebview.framework\/webview_flutter_wkwebview",
    "name" : "webview_flutter_wkwebview",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4390862848,
    "CFBundleShortVersionString" : "0.0.1",
    "CFBundleIdentifier" : "org.cocoapods.workmanager",
    "size" : 163840,
    "uuid" : "f1b859ba-2861-3133-a066-20a0754936f2",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/24942F86-3060-4B2A-962B-60BD9614F1F4\/data\/Containers\/Bundle\/Application\/13FF1FEF-7257-49BB-8287-1FCD0810CD0E\/Runner.app\/Frameworks\/workmanager.framework\/workmanager",
    "name" : "workmanager",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4458266624,
    "CFBundleShortVersionString" : "1.0",
    "CFBundleIdentifier" : "io.flutter.flutter",
    "size" : 35110912,
    "uuid" : "4c4c4445-5555-3144-a11f-921ceb984ad8",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/24942F86-3060-4B2A-962B-60BD9614F1F4\/data\/Containers\/Bundle\/Application\/13FF1FEF-7257-49BB-8287-1FCD0810CD0E\/Runner.app\/Frameworks\/Flutter.framework\/Flutter",
    "name" : "Flutter",
    "CFBundleVersion" : "1.0"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4380491776,
    "size" : 32768,
    "uuid" : "f77d8041-529c-3659-ac12-4821c1c301fc",
    "path" : "\/usr\/lib\/system\/libsystem_platform.dylib",
    "name" : "libsystem_platform.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4390387712,
    "size" : 245760,
    "uuid" : "f30ad29e-a6a0-3f43-8f6e-3770f040794d",
    "path" : "\/usr\/lib\/system\/libsystem_kernel.dylib",
    "name" : "libsystem_kernel.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4382375936,
    "size" : 65536,
    "uuid" : "7d5c0390-4e96-3458-ad9c-5c41b02beefd",
    "path" : "\/usr\/lib\/system\/libsystem_pthread.dylib",
    "name" : "libsystem_pthread.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4394450944,
    "size" : 49152,
    "uuid" : "56878cbd-4b61-3d67-a830-23a1b2beaf59",
    "path" : "\/Volumes\/VOLUME\/*\/libobjc-trampolines.dylib",
    "name" : "libobjc-trampolines.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 6443491328,
    "size" : 509852,
    "uuid" : "d8eab09a-74cb-346d-b14e-3187db1c40db",
    "path" : "\/Volumes\/VOLUME\/*\/libsystem_c.dylib",
    "name" : "libsystem_c.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 6445182976,
    "size" : 114688,
    "uuid" : "649076f2-9c2b-3e8e-833b-d246ac43869e",
    "path" : "\/Volumes\/VOLUME\/*\/libc++abi.dylib",
    "name" : "libc++abi.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 6442876928,
    "size" : 248096,
    "uuid" : "b10e226d-4fab-3450-9a4c-071d3d0edf3a",
    "path" : "\/Volumes\/VOLUME\/*\/libobjc.A.dylib",
    "name" : "libobjc.A.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 6446211072,
    "CFBundleShortVersionString" : "6.9",
    "CFBundleIdentifier" : "com.apple.CoreFoundation",
    "size" : 4292608,
    "uuid" : "ae27f481-c1fa-359c-b04c-af9cda7655ff",
    "path" : "\/Volumes\/VOLUME\/*\/CoreFoundation.framework\/CoreFoundation",
    "name" : "CoreFoundation",
    "CFBundleVersion" : "3502"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 6444003328,
    "size" : 281504,
    "uuid" : "990151a6-fd18-3496-84e3-f565307fbc2f",
    "path" : "\/Volumes\/VOLUME\/*\/libdispatch.dylib",
    "name" : "libdispatch.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 6727671808,
    "CFBundleShortVersionString" : "1.0",
    "CFBundleIdentifier" : "com.apple.GraphicsServices",
    "size" : 33280,
    "uuid" : "80b30bb2-e6e1-317e-b798-ea590de713a8",
    "path" : "\/Volumes\/VOLUME\/*\/GraphicsServices.framework\/GraphicsServices",
    "name" : "GraphicsServices",
    "CFBundleVersion" : "1.0"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 6524219392,
    "CFBundleShortVersionString" : "1.0",
    "CFBundleIdentifier" : "com.apple.UIKitCore",
    "size" : 30851776,
    "uuid" : "f5406608-aa34-30ba-8494-0a8b531792f5",
    "path" : "\/Volumes\/VOLUME\/*\/UIKitCore.framework\/UIKitCore",
    "name" : "UIKitCore",
    "CFBundleVersion" : "8506.1.101"
  },
  {
    "size" : 0,
    "source" : "A",
    "base" : 0,
    "uuid" : "00000000-0000-0000-0000-000000000000"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 6451023872,
    "CFBundleShortVersionString" : "6.9",
    "CFBundleIdentifier" : "com.apple.Foundation",
    "size" : 12254784,
    "uuid" : "48eb0271-c8d1-359a-bd56-bcf3e7e37dc5",
    "path" : "\/Volumes\/VOLUME\/*\/Foundation.framework\/Foundation",
    "name" : "Foundation",
    "CFBundleVersion" : "3502"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 6443233280,
    "size" : 244384,
    "uuid" : "021c0bf8-edd4-395a-b281-2cc7a26f742f",
    "path" : "\/Volumes\/VOLUME\/*\/libxpc.dylib",
    "name" : "libxpc.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 9341526016,
    "CFBundleShortVersionString" : "368.12",
    "CFBundleIdentifier" : "com.apple.MTLSimDriver",
    "size" : 211392,
    "uuid" : "8087c5f9-8281-36e4-90a5-b6b0d0f39264",
    "path" : "\/Volumes\/VOLUME\/*\/MTLSimDriver.framework\/MTLSimDriver",
    "name" : "MTLSimDriver",
    "CFBundleVersion" : "368.12"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 6973976576,
    "CFBundleShortVersionString" : "368.12",
    "CFBundleIdentifier" : "com.apple.Metal",
    "size" : 2135616,
    "uuid" : "79a48c4f-daa0-307d-a147-6125da3a34a4",
    "path" : "\/Volumes\/VOLUME\/*\/Metal.framework\/Metal",
    "name" : "Metal",
    "CFBundleVersion" : "368.12"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 7360225280,
    "CFBundleShortVersionString" : "1.0",
    "CFBundleIdentifier" : "com.apple.audio.caulk",
    "size" : 153056,
    "uuid" : "7f22c3c4-1a93-34dd-bf46-522f8d2a0e77",
    "path" : "\/Volumes\/VOLUME\/*\/caulk.framework\/caulk",
    "name" : "caulk"
  }
],
  "sharedCache" : {
  "base" : 6442450944,
  "size" : 3915218944,
  "uuid" : "197e44a5-4546-37f4-8533-db09ce21f1e6"
},
  "vmSummary" : "ReadOnly portion of Libraries: Total=1.6G resident=0K(0%) swapped_out_or_unallocated=1.6G(100%)\nWritable regions: Total=1.8G written=2072K(0%) resident=2072K(0%) swapped_out=0K(0%) unallocated=1.8G(100%)\n\n                                VIRTUAL   REGION \nREGION TYPE                        SIZE    COUNT (non-coalesced) \n===========                     =======  ======= \nActivity Tracing                   256K        1 \nColorSync                           64K        4 \nFoundation                          16K        1 \nIOSurface                           16K        1 \nKernel Alloc Once                   32K        1 \nMALLOC                             1.6G       51 \nMALLOC guard page                  192K       12 \nSTACK GUARD                       56.6M       38 \nStack                             36.1M       38 \nVM_ALLOCATE                      136.4M      328 \n__DATA                            33.3M      821 \n__DATA_CONST                      94.8M      839 \n__DATA_DIRTY                       139K       11 \n__FONT_DATA                        2352        1 \n__LINKEDIT                       709.8M       52 \n__OBJC_RO                         61.3M        1 \n__OBJC_RW                         2727K        1 \n__TEXT                           934.4M      854 \n__TPRO_CONST                       148K        2 \ndyld private memory                2.5G       14 \nmapped file                      138.1M       22 \npage table in kernel              2072K        1 \nshared memory                       48K        2 \n===========                     =======  ======= \nTOTAL                              6.2G     3096 \n",
  "legacyInfo" : {
  "threadTriggered" : {

  }
},
  "logWritingSignature" : "aac8724596e61c8adca25142c38e6d281eac1d9a",
  "trialInfo" : {
  "rollouts" : [
    {
      "rolloutId" : "645197bf528fbf3c3af54105",
      "factorPackIds" : [
        "663e65b4a1526e1ca0e288a1"
      ],
      "deploymentId" : 240000002
    },
    {
      "rolloutId" : "652eff3d1bce5442b8d753c9",
      "factorPackIds" : [

      ],
      "deploymentId" : 250000005
    }
  ],
  "experiments" : [
    {
      "treatmentId" : "cc637087-5f6a-4428-bea8-ad080e9a6232",
      "experimentId" : "68630692e1874a7adc676348",
      "deploymentId" : 500000001
    }
  ]
}
}

Model: MacBookPro18,3, BootROM 13822.0.194.0.3, proc 10:8:2 processors, 32 GB, SMC 
Graphics: Apple M1 Pro, Apple M1 Pro, Built-In
Display: Color LCD, 3024 x 1964 Retina, Main, MirrorOff, Online
Display: PA279CRV, 5120 x 2880 (5K/UHD+ - Ultra High Definition Plus), MirrorOff, Online
Memory Module: LPDDR5, Hynix
AirPort: spairport_wireless_card_type_wifi (0x14E4, 0x4387), wl0: Jun 21 2025 01:59:16 version 20.10.1175.0.8.7.211 FWID 01-b1af186d
IO80211_driverkit-1525.78 "IO80211_driverkit-1525.78" Jun 30 2025 21:36:17
AirPort: 
Bluetooth: Version (null), 0 services, 0 devices, 0 incoming serial ports
Network Service: Wi-Fi, AirPort, en0
PCI Card: Universal Audio Apollo Twin X - DUO, Other Multimedia, Thunderbolt@3,0,0
Thunderbolt Bus: MacBook Pro, Apple Inc.
Thunderbolt Bus: MacBook Pro, Apple Inc.
Thunderbolt Bus: MacBook Pro, Apple Inc.
Thunderbolt Device: Apollo Twin X, Universal Audio, Inc., 1, 18.6
