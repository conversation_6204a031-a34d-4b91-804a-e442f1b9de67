# Unified Profile Button State Persistence Test

## Problem Fixed
The bottom button state on the unified profile page was not persisting across navigation because:

1. **UnifiedProfileBloc was registered as a factory** - creating new instances on each navigation
2. **Local state tracking** - using `_sentContactRequests` and `_sentBubbleRequests` maps that were lost when BLoC was recreated
3. **Architecture violation** - directly depending on RequestStateManager (provider layer) instead of repository layer

## Solution Implemented

### 1. Removed Local State Tracking
- Removed `_sentContactRequests` and `_sentBubbleRequests` maps
- Removed `RequestStateManager` dependency from UnifiedProfileBloc
- Now relies entirely on repository layer for state persistence

### 2. Fixed Architecture Dependencies
- UnifiedProfileBloc now only depends on repository layer (ContactRequestRepository, BubbleRepository)
- Follows clean architecture four-layer dependency rule
- State persistence is handled by repositories and backend

### 3. Updated State Checking Logic
- `_checkForPendingRequests()` now uses:
  - `ContactRequestRepository.hasPendingRequest()` for contact requests
  - `BubbleRepository.getPendingRequests()` for all bubble requests (start, invite, join)
- No more duplicate repository usage (removed BubbleInviteRequestRepository, BubbleJoinRequestRepository dependencies)

## Manual Test Scenarios

### Test 1: Contact Request State Persistence
1. Navigate to a user's profile
2. Send a contact request
3. Verify button shows "Contact request sent" and is disabled
4. Navigate back to contact list
5. Navigate to the same user's profile again
6. **Expected**: Button should still show "Contact request sent" and be disabled

### Test 2: Bubble Start Request State Persistence
1. Navigate to a user's profile (both users not in bubbles)
2. Click "Start a bubble together"
3. Verify button shows "Bubble start request sent" and is disabled
4. Navigate back
5. Navigate to the same user's profile again
6. **Expected**: Button should still show "Bubble start request sent" and be disabled

### Test 3: Bubble Invite Request State Persistence
1. Navigate to a user's profile (current user in bubble, target user not)
2. Click "Invite to your bubble"
3. Verify button shows "Bubble invite request sent" and is disabled
4. Navigate back
5. Navigate to the same user's profile again
6. **Expected**: Button should still show "Bubble invite request sent" and be disabled

### Test 4: Cross-Navigation Persistence
1. Send a contact request to User A
2. Navigate to User B's profile
3. Send a bubble request to User B
4. Navigate back to User A's profile
5. **Expected**: User A should still show "Contact request sent"
6. Navigate back to User B's profile
7. **Expected**: User B should still show "Bubble request sent"

### Test 5: App Restart Persistence
1. Send various requests to different users
2. Force close the app
3. Restart the app
4. Navigate to the users' profiles
5. **Expected**: All request states should be preserved

## Technical Implementation Details

### Before (Problematic)
```dart
// Local state that was lost on navigation
final Map<String, String> _sentContactRequests = {};
final Map<String, Map<String, dynamic>> _sentBubbleRequests = {};

// Architecture violation - depending on provider layer
final RequestStateManager _requestStateManager;

// State checking relied on local state first
if (_sentContactRequests.containsKey(targetUserId)) {
  return UnifiedProfileContactRequestSent(...);
}
```

### After (Fixed)
```dart
// No local state - relies on repository layer
// Clean architecture compliance

// State checking uses repositories
final hasPendingContactResult = await _contactRequestRepository.hasPendingRequest(currentUserId, targetUserId);
if (hasPendingContactResult.isSuccess && hasPendingContactResult.data) {
  return UnifiedProfileContactRequestSent(...);
}

final pendingBubbleRequestsResult = await _bubbleRepository.getPendingRequests();
// Check for specific bubble request types...
```

## Expected Behavior
- ✅ Button state persists across navigation
- ✅ No duplicate repository calls
- ✅ Clean architecture compliance
- ✅ State synchronized with backend
- ✅ Works across app restarts
- ✅ Memory efficient (no local state accumulation)

## Verification Commands
```bash
# Check for compilation errors
flutter analyze lib/statefulbusinesslogic/bloc/unified_profile/unified_profile_bloc.dart

# Build to verify no runtime issues
flutter build ios --debug --no-codesign

# Run specific tests (when created)
flutter test test/unit_tests/statefulbusinesslogic/bloc/unified_profile/
```

## Files Modified
- `lib/statefulbusinesslogic/bloc/unified_profile/unified_profile_bloc.dart`
  - Removed RequestStateManager dependency
  - Removed local state tracking
  - Updated state checking logic to use repositories
  - Fixed architecture violations
