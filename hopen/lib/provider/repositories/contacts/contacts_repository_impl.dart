import '../../../repositories/contacts/contacts_repository.dart';
import '../../../statefulbusinesslogic/core/error/exceptions.dart';
import '../../../statefulbusinesslogic/core/models/bubble_membership_status.dart';
import '../../../statefulbusinesslogic/core/models/contact.dart';
import '../../../statefulbusinesslogic/core/models/relationship_type.dart';
import '../../datasources/contacts_remote_datasource.dart';
import '../../datasources/http_remote_datasource.dart';
import '../../models/api_models.dart';
import '../../services/api/http_api_service.dart';

class ContactsRepositoryImpl implements ContactsRepository {

  ContactsRepositoryImpl({
    required this.remoteDataSource,
    required this.httpDataSource,
    required this.apiService,
  });
  final ContactsRemoteDataSource remoteDataSource;
  final HttpRemoteDataSource httpDataSource;
  final HttpApiService apiService;

  @override
  Future<List<UserContact>> getContacts() async {
    try {
      // Get current user profile first (this is working)
      final currentUser = await httpDataSource.getCurrentUserProfile();
      print('DEBUG: getContacts - current user: ${currentUser.id}');
      
      // Try to get existing contacts (contact relationships)
      List<ApiContact> apiContacts = [];
      try {
        apiContacts = await remoteDataSource.getContacts();
        print('DEBUG: getContacts - successfully fetched ${apiContacts.length} contact relationships');
      } catch (e) {
        print('DEBUG: getContacts - failed to fetch contact relationships: $e');
        // Continue with empty contacts list
      }

      // Convert contact relationships to user contacts by fetching user profiles
      List<UserContact> userContacts = [];
      for (final apiContact in apiContacts) {
        try {
          // The API returns relationship data where senderId is the contact's user ID
          final contactUserId = apiContact.senderId ?? apiContact.id;
          print('DEBUG: getContacts - fetching profile for contact user ID: $contactUserId');
          
          // Fetch the actual user profile for this contact
          final userProfile = await httpDataSource.getUserProfile(contactUserId);
          print('DEBUG: getContacts - fetched profile: ${userProfile.firstName} ${userProfile.lastName}');
          
          // Create UserContact from the user profile and relationship data
          final userContact = UserContact(
            id: userProfile.id,
            name: '${userProfile.firstName ?? ''} ${userProfile.lastName ?? ''}'.trim(),
            username: userProfile.username,
            imageUrl: userProfile.profilePictureUrl,
            relationshipType: _mapApiContactStatus(apiContact.status),
            bubbleStatus: _mapBubbleStatus(userProfile.bubbleStatus),
            isOnline: userProfile.isOnline ?? false,
          );
          
          userContacts.add(userContact);
        } catch (e) {
          print('DEBUG: getContacts - failed to fetch profile for contact ${apiContact.senderId}: $e');
          // Continue with other contacts
        }
      }

      // Try to get users (potential contacts) using search endpoint
      List<ApiUserProfile> potentialContacts = [];
      try {
        // Use the search endpoint with a broad query to get all users
        potentialContacts = await httpDataSource.searchUsers(query: 'a', pageSize: 50);
        print('DEBUG: getContacts - successfully fetched ${potentialContacts.length} potential contacts via search');
      } catch (e) {
        print('DEBUG: getContacts - failed to fetch users via search: $e');
        // Continue with empty potential contacts list
      }

      // Convert additional users to contacts with no relationship
      for (final apiUser in potentialContacts) {
        // Skip current user
        if (apiUser.id == currentUser.id) continue;
        
        // Skip users who are already contacts
        if (userContacts.any((contact) => contact.id == apiUser.id)) continue;
        
        final userContact = UserContact(
          id: apiUser.id,
          name: '${apiUser.firstName ?? ''} ${apiUser.lastName ?? ''}'.trim(),
          username: apiUser.username,
          imageUrl: apiUser.profilePictureUrl,
          relationshipType: RelationshipType.none,
          bubbleStatus: _mapBubbleStatus(apiUser.bubbleStatus),
          isOnline: apiUser.isOnline ?? false,
        );
        
        userContacts.add(userContact);
      }

      print('DEBUG: getContacts - returning ${userContacts.length} total contacts (${apiContacts.length} existing contacts + ${potentialContacts.length} potential contacts)');
      return userContacts;
    } catch (e) {
      print('DEBUG: ContactsRepositoryImpl.getContacts error: $e');
      return [];
    }
  }

  @override
  Future<void> sendContactRequest(String userId) async {
    try {
      // Check if already friends or request exists
      // final currentUser = await remoteDataSource.getCurrentUser();
      
      // if (currentUser.friendIds.contains(userId)) {
      //   throw Exception('User is already a friend');
      // }
      
      // if (currentUser.pendingSentContactRequestIds.contains(userId)) {
      //   throw Exception('Contact request already sent');
      // }
      
      // if (currentUser.blockedUserIds.contains(userId)) {
      //   throw Exception('Cannot send request to blocked user');
      // }

      final request = SendContactRequestRequest(
        recipientId: userId,
        message: 'Contact request',
      );
      await remoteDataSource.sendContactRequest(request);
    } on ServerException {
      rethrow;
    } catch (e) {
      throw ServerException(message: e.toString());
    }
  }

  @override
  Future<void> sendBubbleRequest({
    required String contactId,
    required String requestType,
  }) async {
    try {
      // For now, use contactId as userId and ignore requestType
  // final userId = contactId;
      
      // Placeholder implementation - would need proper bubble invitation API
      // await remoteDataSource.inviteToBubble(bubbleId, {'targetUserId': userId});
      
      // For now, just validate the user exists
      // final targetUser = await remoteDataSource.getUserById(userId);
      // if (targetUser == null) {
      //   throw Exception('Target user not found');
      // }
    } catch (e) {
      throw Exception('Failed to send bubble request: $e');
    }
  }

  @override
  Future<void> addContact(
    String userId, {
    RelationshipType relationshipType = RelationshipType.contact,
  }) async {
    try {
      // Use the existing contact request system to add contacts
      final request = SendContactRequestRequest(
        recipientId: userId,
        message: 'Contact request',
      );
      await remoteDataSource.sendContactRequest(request);
    } on ServerException {
      rethrow;
    } catch (e) {
      throw ServerException(message: e.toString());
    }
  }

  @override
  Future<void> removeContact(String contactId) async {
    try {
      await remoteDataSource.removeContact(contactId);
    } on ServerException {
      rethrow;
    } catch (e) {
      throw ServerException(message: e.toString());
    }
  }

  @override
  Future<void> blockContact(String contactId) async {
    try {
      await apiService.blockContact(contactId);
    } on ServerException {
      rethrow;
    } catch (e) {
      throw ServerException(message: e.toString());
    }
  }

  @override
  Future<void> updateContactRelationship(
    String contactId,
    RelationshipType relationshipType,
  ) async {
    try {
      // For now, this is a placeholder implementation
      // The actual implementation would depend on having an update contact endpoint
      switch (relationshipType) {
        case RelationshipType.blocked:
          await blockContact(contactId);
          break;
        case RelationshipType.contact:
        case RelationshipType.friend:
          // Accept contact request if pending, otherwise no action needed
          try {
            final acceptRequest = AcceptContactRequestRequest(contactRequestId: contactId);
            await remoteDataSource.acceptContactRequest(acceptRequest);
          } catch (e) {
            // If accept fails, the contact might already be accepted
            // This is acceptable for this implementation
          }
          break;
        case RelationshipType.none:
          await removeContact(contactId);
          break;
        case RelationshipType.self:
          // No action needed for self relationship
          break;
        case RelationshipType.bubbler:
          // No action needed for bubbler relationship
          break;
        case RelationshipType.contactRequestSent:
        case RelationshipType.contactRequestReceived:
        case RelationshipType.friendRequestSent:
        case RelationshipType.friendRequestReceived:
          // Handle request-related relationships
          break;
      }
    } on ServerException {
      rethrow;
    } catch (e) {
      throw ServerException(message: e.toString());
    }
  }

  // Helper method to convert API model to domain model
  UserContact _mapApiContactToUserContact(ApiContact apiContact) {
    // The API returns contact relationship data where:
    // - id: relationship ID (not user ID)
    // - senderId: the actual user ID of the contact
    // - receiverId: the current user's ID
    // We need to use senderId as the contact's user ID, but fall back to original ID if null
    final contactUserId = apiContact.senderId ?? apiContact.id;
    
    // Use status field to determine relationship type, fallback to relationshipType field
    final relationshipType = apiContact.status != null 
        ? _mapApiContactStatus(apiContact.status)
        : _mapStringToRelationshipType(apiContact.relationshipType ?? 'contact');
    
    return UserContact(
      id: contactUserId,
      name: '${apiContact.firstName ?? ''} ${apiContact.lastName ?? ''}'.trim(),
      username: apiContact.username,
      imageUrl: apiContact.profilePictureUrl,
      relationshipType: relationshipType,
      isOnline: apiContact.isOnline ?? false,
      contactSince: apiContact.createdAt ?? DateTime.now(),
      bubbleStatus: _mapBubbleStatus(apiContact.status),
    );
  }

  // Helper method to convert string to RelationshipType enum
  RelationshipType _mapStringToRelationshipType(String type) {
    switch (type.toLowerCase()) {
      case 'friend':
        return RelationshipType.friend;
      case 'contact':
        return RelationshipType.contact;
      case 'blocked':
        return RelationshipType.blocked;
      case 'none':
        return RelationshipType.none;
      case 'contactrequestsent':
      case 'contact_request_sent':
      case 'pending':
        return RelationshipType.contactRequestSent;
      case 'contactrequestreceived':
      case 'contact_request_received':
        return RelationshipType.contactRequestReceived;
      default:
        return RelationshipType.contact;
    }
  }

  // Helper method to map bubble status from API
  BubbleMembershipStatus _mapBubbleStatus(String? bubbleStatus) {
    if (bubbleStatus == null) return BubbleMembershipStatus.noBubble;

    switch (bubbleStatus.toLowerCase()) {
      case 'active':
      case 'joined':
      case 'in_bubble':
        return BubbleMembershipStatus.notFullBubble; // User is in a bubble
      case 'full':
      case 'full_bubble':
        return BubbleMembershipStatus.fullBubble; // User is in a full bubble
      case 'inactive':
      case 'no_bubble':
      case 'left':
      default:
        return BubbleMembershipStatus.noBubble;
    }
  }

  // Helper method to map API contact status to RelationshipType
  RelationshipType _mapApiContactStatus(String? status) {
    switch (status?.toLowerCase()) {
      case 'accepted':
        return RelationshipType.contact;
      case 'pending':
        return RelationshipType.contactRequestSent;
      case 'friend':
        return RelationshipType.friend;
      case 'blocked':
        return RelationshipType.blocked;
      case 'none':
        return RelationshipType.none;
      default:
        return RelationshipType.contact; // Default to contact for accepted relationships
    }
  }

  // Helper method to convert API user profile to UserContact
  UserContact _mapApiUserProfileToUserContact(ApiUserProfile apiUser) => UserContact(
      id: apiUser.id,
      name: '${apiUser.firstName ?? ''} ${apiUser.lastName ?? ''}'.trim(),
      username: apiUser.username,
      imageUrl: apiUser.profilePictureUrl,
      relationshipType: RelationshipType.none, // Users with no relationship
      isOnline: apiUser.isOnline ?? false,
      contactSince: DateTime.now(),
      bubbleStatus: BubbleMembershipStatus.noBubble,
    );

  // ==========================================================================
  // Search Users
  // ==========================================================================

  @override
  Future<List<UserContact>> searchUsers(String query) async {
    print('🔍 ContactsRepository: searchUsers called with query: "$query"');
    try {
      // Fetch existing contacts, search results, and current user profile in parallel
      final futures = <Future>[
        remoteDataSource.getContacts(),
        httpDataSource.getUsers(search: query.trim()),
        httpDataSource.getCurrentUserProfile(),
      ];

      print('🔍 ContactsRepository: Making parallel API calls for search');

      // Execute calls individually to handle errors gracefully
      List<ApiContact> apiContacts = [];
      PaginatedUserResponse? paginatedUsers;
      ApiUserProfile? currentUser;

      // 1. Try to get existing contacts (optional - don't fail if this doesn't work)
      try {
        apiContacts = await remoteDataSource.getContacts();
        print('🔍 ContactsRepository: getContacts succeeded with ${apiContacts.length} contacts');
      } catch (e) {
        print('🔍 ContactsRepository: getContacts failed: $e - continuing without contacts');
        // Continue with empty contacts list - this is not critical for search
      }

      // 2. Get user search results (critical - this is the main functionality)
      try {
        paginatedUsers = await httpDataSource.getUsers(search: query.trim());
        print('🔍 ContactsRepository: getUsers succeeded with ${paginatedUsers.data.length} users');
      } catch (e) {
        print('🔍 ContactsRepository: getUsers failed: $e');
        // If user search fails, return empty list
        return [];
      }

      // 3. Try to get current user profile (optional - for filtering)
      try {
        currentUser = await httpDataSource.getCurrentUserProfile();
        print('🔍 ContactsRepository: getCurrentUserProfile succeeded for user ${currentUser.id}');
      } catch (e) {
        print('🔍 ContactsRepository: getCurrentUserProfile failed: $e - continuing without user filtering');
        // Continue without current user filtering
      }

      if (paginatedUsers == null) {
        print('🔍 ContactsRepository: No user search results available');
        return [];
      }

      print('🔍 ContactsRepository: API calls completed. Search returned ${paginatedUsers.data.length} users');

      // Filter out current user if available
      String? currentUserId = currentUser?.id;

      // Convert contacts to UserContact objects and exclude current user
      final contactsList = apiContacts
          .map(_mapApiContactToUserContact)
          .where((contact) => currentUserId == null || contact.id != currentUserId)
          .toList();

      final usersList = paginatedUsers.data
          .map(_mapApiUserProfileToUserContact)
          .toList();

      final contactIds = contactsList.map((c) => c.id).toSet();

      // If currentUserId is still null after current user lookup, proceed without filtering self.

      final nonContactUsers = usersList.where((u) =>
          !contactIds.contains(u.id) && (currentUserId == null || u.id != currentUserId)).toList();

      final allUsers = [...contactsList, ...nonContactUsers];

      print('🔍 ContactsRepository: Returning ${allUsers.length} total users (${contactsList.length} contacts + ${nonContactUsers.length} non-contacts)');
      return allUsers;
    } catch (e) {
      print('DEBUG: searchUsers error: $e');
      return <UserContact>[];
    }
  }
} 