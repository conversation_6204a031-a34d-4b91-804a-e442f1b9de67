<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CLIENT_ID</key>
	<string>257996495540-8555p6h5grihi9qmgrts661sro96kfd7.apps.googleusercontent.com</string>
	<key>REVERSED_CLIENT_ID</key>
	<string>com.googleusercontent.apps.257996495540-8555p6h5grihi9qmgrts661sro96kfd7</string>
	<key>ANDROID_CLIENT_ID</key>
	<string>257996495540-olsk4864agsveq93ihnmvjt6h260h5un.apps.googleusercontent.com</string>
	<key>API_KEY</key>
	<string>AIzaSyAGc_p8eZ3sSU3mVjW2jYJODcC6r7_Fm7I</string>
	<key>GCM_SENDER_ID</key>
	<string>257996495540</string>
	<key>PLIST_VERSION</key>
	<string>1</string>
	<key>BUNDLE_ID</key>
	<string>com.hopenapp.hopen</string>
	<key>PROJECT_ID</key>
	<string>hopen-id</string>
	<key>STORAGE_BUCKET</key>
	<string>hopen-id.firebasestorage.app</string>
	<key>IS_ADS_ENABLED</key>
	<false></false>
	<key>IS_ANALYTICS_ENABLED</key>
	<false></false>
	<key>IS_APPINVITE_ENABLED</key>
	<true></true>
	<key>IS_GCM_ENABLED</key>
	<true></true>
	<key>IS_SIGNIN_ENABLED</key>
	<true></true>
	<key>GOOGLE_APP_ID</key>
	<string>1:257996495540:ios:912b2da451dcd7e98f95e1</string>
</dict>
</plist>