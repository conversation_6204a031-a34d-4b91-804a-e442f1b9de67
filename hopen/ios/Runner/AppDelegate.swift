import UIKit
import Firebase
import FirebaseMessaging
import CallKit
import AVFoundation
import PushKit
import Flutter

@main
@objc class AppDelegate: FlutterAppDelegate, CXProviderDelegate, PKPushRegistryDelegate {
  private var callProvider: CXProvider?
  private var callController: CXCallController?
  private var callUUID: UUID?
  private var callMethodChannel: FlutterMethodChannel?
  private var voipRegistry: PKPushRegistry?
  
  override func application(
    _ application: UIApplication,
    didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?
  ) -> Bool {
    // Register for remote notifications first
    if #available(iOS 10.0, *) {
      UNUserNotificationCenter.current().delegate = self
      
      let authOptions: UNAuthorizationOptions = [.alert, .badge, .sound]
      UNUserNotificationCenter.current().requestAuthorization(options: authOptions) { _, _ in }
    } else {
      let settings: UIUserNotificationSettings = UIUserNotificationSettings(types: [.alert, .badge, .sound], categories: nil)
      application.registerUserNotificationSettings(settings)
    }
    
    application.registerForRemoteNotifications()
    
    let controller = window?.rootViewController as! FlutterViewController
    setupCallMethodChannel(controller)
    
    // Set up CallKit
    setupCallKit()
    
    // Set up VoIP push notifications
    setupVoIPPush()
    
    GeneratedPluginRegistrant.register(with: self)
    
    // Firebase will be initialized from Flutter/Dart side
    // Set up FCM delegate after Flutter initializes Firebase
    DispatchQueue.main.async {
      // Check if GoogleService-Info.plist exists in the bundle
      guard let path = Bundle.main.path(forResource: "GoogleService-Info", ofType: "plist") else {
        print("ERROR: GoogleService-Info.plist not found in app bundle")
        return
      }

      // Wait for Firebase to be initialized from Flutter side, then set delegate
      DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
        if FirebaseApp.app() != nil {
          Messaging.messaging().delegate = self
          print("FCM delegate set successfully after Flutter initialization")
        } else {
          print("Firebase not yet initialized by Flutter")
        }
      }
    }
    
    return super.application(application, didFinishLaunchingWithOptions: launchOptions)
  }
  
  private func setupCallMethodChannel(_ controller: FlutterViewController) {
    callMethodChannel = FlutterMethodChannel(name: "com.example.hopen/call", binaryMessenger: controller.binaryMessenger)
  }
  
  private func setupCallKit() {
    let providerConfiguration = CXProviderConfiguration(localizedName: "Hopen")
    providerConfiguration.supportsVideo = true
    providerConfiguration.maximumCallGroups = 1
    providerConfiguration.maximumCallsPerCallGroup = 1
    providerConfiguration.supportedHandleTypes = [.generic]
    providerConfiguration.ringtoneSound = "incoming_call.mp3" // Add this sound to your bundle
    
    callProvider = CXProvider(configuration: providerConfiguration)
    callProvider?.setDelegate(self, queue: nil)
    callController = CXCallController()
  }
  
  private func setupVoIPPush() {
    voipRegistry = PKPushRegistry(queue: DispatchQueue.main)
    voipRegistry?.delegate = self
    voipRegistry?.desiredPushTypes = [.voIP]
  }
  
  // MARK: - Remote Notification Handling
  
  override func application(_ application: UIApplication, didReceiveRemoteNotification userInfo: [AnyHashable: Any], fetchCompletionHandler completionHandler: @escaping (UIBackgroundFetchResult) -> Void) {
    // Process FCM message
    if let messageType = userInfo["type"] as? String, messageType == "incoming_call" {
      handleIncomingCallNotification(userInfo)
      completionHandler(.newData)
    } else {
      // Let Firebase handle other notifications
      completionHandler(.noData)
    }
  }
  
  private func handleIncomingCallNotification(_ userInfo: [AnyHashable: Any]) {
    print("Received incoming call notification: \(userInfo)")
    
    guard let callId = userInfo["call_id"] as? String,
          let callerId = userInfo["caller_id"] as? String else {
      print("Missing required call information")
      return
    }
    
    let callerName = userInfo["caller_name"] as? String ?? "Unknown"
    let callerAvatar = userInfo["caller_avatar"] as? String
    let targetType = userInfo["target_type"] as? String ?? "user"
    let targetId = userInfo["target_id"] as? String
    let targetName = userInfo["target_name"] as? String
    let targetAvatarUrl = userInfo["target_avatar_url"] as? String
    let sdpOffer = userInfo["sdp_offer"] as? String
    let isVideoOffered = userInfo["video_offered"] as? Bool ?? false
    let isAudioOffered = userInfo["audio_offered"] as? Bool ?? true
    let isScreenShareOffered = userInfo["screen_share_offered"] as? Bool ?? false
    
    // Report incoming call to CallKit
    reportIncomingCall(
      id: callId,
      callerId: callerId,
      callerName: callerName,
      hasVideo: isVideoOffered
    )
    
    // Save call information for when user answers
    self.callUUID = UUID(uuidString: callId) ?? UUID()
    
    // Prepare data for Flutter
    let callData: [String: Any] = [
      "call_id": callId,
      "caller_id": callerId,
      "caller_name": callerName,
      "caller_avatar": callerAvatar as Any,
      "is_group": targetType == "bubble",
      "group_id": targetId as Any,
      "group_name": targetName as Any,
      "group_avatar_url": targetAvatarUrl as Any,
      "remote_offer_sdp": sdpOffer as Any,
      "is_video_offered": isVideoOffered,
      "is_audio_offered": isAudioOffered,
      "is_screen_share_offered": isScreenShareOffered
    ]
    
    // Send to Flutter if app is already running
    DispatchQueue.main.async {
      self.callMethodChannel?.invokeMethod("incomingCall", arguments: callData)
    }
  }
  
  // MARK: - CallKit Integration
  
  private func reportIncomingCall(id: String, callerId: String, callerName: String, hasVideo: Bool) {
    let callUUID = UUID(uuidString: id) ?? UUID()
    self.callUUID = callUUID
    
    let callUpdate = CXCallUpdate()
    callUpdate.remoteHandle = CXHandle(type: .generic, value: callerId)
    callUpdate.localizedCallerName = callerName
    callUpdate.hasVideo = hasVideo
    
    callProvider?.reportNewIncomingCall(with: callUUID, update: callUpdate) { error in
      if let error = error {
        print("Failed to report incoming call: \(error.localizedDescription)")
      } else {
        print("Incoming call reported successfully")
        
        // For iOS 13+ we need to request audio session activation
        if #available(iOS 13.0, *) {
          self.requestAudioSession()
        }
      }
    }
  }
  
  private func requestAudioSession() {
    let audioSession = AVAudioSession.sharedInstance()
    do {
      try audioSession.setCategory(.playAndRecord, mode: .voiceChat)
      try audioSession.setActive(true)
    } catch {
      print("Failed to set up audio session: \(error.localizedDescription)")
    }
  }
  
  // MARK: - CXProviderDelegate
  
  func providerDidReset(_ provider: CXProvider) {
    print("Provider did reset")
    // Reset any internal state
  }
  
  func provider(_ provider: CXProvider, perform action: CXAnswerCallAction) {
    print("Call answered via CallKit")
    
    // For iOS 13+ we need to request audio session activation
    if #available(iOS 13.0, *) {
      requestAudioSession()
    }
    
    // Notify Flutter that the call was answered
    if let callUUID = self.callUUID {
      DispatchQueue.main.async {
        self.callMethodChannel?.invokeMethod("callAnswered", arguments: ["call_id": callUUID.uuidString])
      }
    }
    
    action.fulfill()
  }
  
  func provider(_ provider: CXProvider, perform action: CXEndCallAction) {
    print("Call ended via CallKit")
    
    // Notify Flutter that the call was ended
    if let callUUID = self.callUUID {
      DispatchQueue.main.async {
        self.callMethodChannel?.invokeMethod("callEnded", arguments: ["call_id": callUUID.uuidString])
      }
    }
    
    self.callUUID = nil
    action.fulfill()
  }
  
  func provider(_ provider: CXProvider, didActivate audioSession: AVAudioSession) {
    print("Audio session activated")
  }
  
  func provider(_ provider: CXProvider, didDeactivate audioSession: AVAudioSession) {
    print("Audio session deactivated")
  }
  
  // MARK: - PKPushRegistryDelegate
  
  func pushRegistry(_ registry: PKPushRegistry, didUpdate pushCredentials: PKPushCredentials, for type: PKPushType) {
    if type == .voIP {
      print("VoIP push credentials updated: \(pushCredentials.token.map { String(format: "%02.2hhx", $0) }.joined())")
      // Send these credentials to your backend for VoIP push notifications
    }
  }
  
  func pushRegistry(_ registry: PKPushRegistry, didReceiveIncomingPushWith payload: PKPushPayload, for type: PKPushType, completion: @escaping () -> Void) {
    if type == .voIP {
      print("Received VoIP push notification: \(payload.dictionaryPayload)")
      
      // Process the incoming call
      let userInfo = payload.dictionaryPayload
      if userInfo["type"] as? String == "incoming_call" {
        handleIncomingCallNotification(userInfo)
      }
      
      // IMPORTANT: You must call the completion handler when finished processing the VoIP push
      completion()
    }
  }
}

// MARK: - MessagingDelegate

extension AppDelegate: MessagingDelegate {
  func messaging(_ messaging: Messaging, didReceiveRegistrationToken fcmToken: String?) {
    print("Firebase registration token: \(String(describing: fcmToken))")
    
    let dataDict: [String: String] = ["token": fcmToken ?? ""]
    NotificationCenter.default.post(
      name: Notification.Name("FCMToken"),
      object: nil,
      userInfo: dataDict
    )
  }
}
